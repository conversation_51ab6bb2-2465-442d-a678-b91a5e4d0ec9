import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'
import NotificationCenter from './NotificationCenter'
import {
  HomeIcon,
  ChartBarIcon,
  BriefcaseIcon,
  CubeIcon,
  DocumentTextIcon,
  CreditCardIcon,
  BanknotesIcon,
  UsersIcon,
  ReceiptTaxIcon,
  DocumentChartBarIcon,
  CpuChipIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline'

const navigation = [
  { name: 'لوحة التحكم', href: '/dashboard', icon: HomeIcon },
  { name: 'المحاسبة العامة', href: '/accounting', icon: ChartBarIcon },
  { name: 'إدارة المشاريع', href: '/projects', icon: BriefcaseIcon },
  { name: 'المخزون', href: '/inventory', icon: CubeIcon },
  { name: 'الفواتير', href: '/invoices', icon: DocumentTextIcon },
  { name: 'المدفوعات', href: '/payments', icon: CreditCardIcon },
  { name: 'الخزينة والبنوك', href: '/banking', icon: BanknotesIcon },
  { name: 'الموارد البشرية', href: '/hr', icon: UsersIcon },
  { name: 'الضرائب', href: '/tax', icon: ReceiptTaxIcon },
  { name: 'التقارير', href: '/reports', icon: DocumentChartBarIcon },
  { name: 'التحليلات الذكية', href: '/ai-analytics', icon: CpuChipIcon },
]

function Layout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const location = useLocation()
  const { user, logout } = useAuthStore()

  const handleLogout = async () => {
    await logout()
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 right-0 flex w-64 flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <h1 className="text-xl font-bold text-gray-900">نظام إدارة المقاولات</h1>
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => {
              const isActive = location.pathname.startsWith(item.href)
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={isActive ? 'nav-link-active' : 'nav-link-inactive'}
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon className="ml-3 h-5 w-5" />
                  {item.name}
                </Link>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:right-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 shadow-lg">
          <div className="flex h-16 shrink-0 items-center">
            <h1 className="text-xl font-bold text-gray-900">نظام إدارة المقاولات</h1>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => {
                    const isActive = location.pathname.startsWith(item.href)
                    return (
                      <li key={item.name}>
                        <Link
                          to={item.href}
                          className={isActive ? 'nav-link-active' : 'nav-link-inactive'}
                        >
                          <item.icon className="ml-3 h-5 w-5" />
                          {item.name}
                        </Link>
                      </li>
                    )
                  })}
                </ul>
              </li>
              <li className="mt-auto">
                <Link
                  to="/settings"
                  className={location.pathname === '/settings' ? 'nav-link-active' : 'nav-link-inactive'}
                >
                  <CogIcon className="ml-3 h-5 w-5" />
                  الإعدادات
                </Link>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pr-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1"></div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <NotificationCenter />

              {/* User menu */}
              <div className="flex items-center gap-x-4">
                <div className="text-sm">
                  <div className="font-medium text-gray-900">{user?.firstName} {user?.lastName}</div>
                  <div className="text-gray-500">{user?.role}</div>
                </div>
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-x-2 rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500"
                >
                  <ArrowRightOnRectangleIcon className="h-4 w-4" />
                  تسجيل الخروج
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

export default Layout
