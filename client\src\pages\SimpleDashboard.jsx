import { Enhanced<PERSON>ard, <PERSON>hancedButton, StatusBadge, ProgressBar } from '../components/ui'

function SimpleDashboard() {
  const handleLogout = () => {
    localStorage.removeItem('token')
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                🏗️ نظام إدارة المقاولات
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                مرحباً، مدير النظام
              </span>
              <EnhancedButton
                variant="secondary"
                icon="⚙️"
                onClick={() => window.location.href = '/settings'}
              >
                الإعدادات
              </EnhancedButton>
              <EnhancedButton
                variant="error"
                icon="🚪"
                onClick={handleLogout}
              >
                تسجيل الخروج
              </EnhancedButton>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <EnhancedCard className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  🎉 مرحباً بك في نظام إدارة المقاولات
                </h2>
                <p className="text-gray-600 mb-4">
                  نظام محاسبي شامل مع الامتثال الضريبي المصري والذكاء الاصطناعي
                </p>
                <div className="flex items-center space-x-4">
                  <StatusBadge status="success">النظام جاهز للاستخدام</StatusBadge>
                  <StatusBadge status="info">آخر تحديث: اليوم</StatusBadge>
                </div>
              </div>
              <div className="hidden md:block">
                <div className="text-6xl opacity-20">🏗️</div>
              </div>
            </div>
          </EnhancedCard>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <EnhancedCard
              clickable
              className="hover:bg-green-50 hover:border-green-200"
              onClick={() => alert('💰 تفاصيل الإيرادات:\n\n• إيرادات المشاريع: 450,000 ج.م\n• إيرادات أخرى: 50,000 ج.م\n• النمو الشهري: +12%\n• متوسط الإيراد الشهري: 125,000 ج.م')}
              padding="p-5"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="text-3xl">💰</div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      إجمالي الإيرادات
                    </dt>
                    <dd className="text-xl font-bold text-green-600">
                      500,000 ج.م
                    </dd>
                    <dd className="text-xs text-green-500 mt-1">
                      ↗️ +12% من الشهر الماضي
                    </dd>
                  </dl>
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="hover:bg-red-50 hover:border-red-200"
              onClick={() => alert('📊 تفاصيل المصروفات:\n\n• مواد البناء: 180,000 ج.م\n• الرواتب: 80,000 ج.م\n• مصروفات عمومية: 40,000 ج.م\n• النمو الشهري: +5%')}
              padding="p-5"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="text-3xl">📊</div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      إجمالي المصروفات
                    </dt>
                    <dd className="text-xl font-bold text-red-600">
                      300,000 ج.م
                    </dd>
                    <dd className="text-xs text-red-500 mt-1">
                      ↗️ +5% من الشهر الماضي
                    </dd>
                  </dl>
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="hover:bg-blue-50 hover:border-blue-200"
              onClick={() => alert('🏗️ تفاصيل المشاريع:\n\n• مشروع الفيلا السكنية: 75% مكتمل\n• مشروع المجمع التجاري: 45% مكتمل\n• مشروع المدرسة: 30% مكتمل\n• مشروع المستشفى: 60% مكتمل\n• مشروع المكاتب: 20% مكتمل')}
              padding="p-5"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="text-3xl">🏗️</div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      المشاريع النشطة
                    </dt>
                    <dd className="text-xl font-bold text-blue-600">
                      5 مشاريع
                    </dd>
                    <dd className="text-xs text-blue-500 mt-2">
                      <ProgressBar value={46} variant="info" size="sm" />
                      <span className="mt-1 block">متوسط التقدم: 46%</span>
                    </dd>
                  </dl>
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="hover:bg-yellow-50 hover:border-yellow-200"
              onClick={() => alert('📈 تفاصيل الأرباح:\n\n• هامش الربح: 40%\n• الربح الشهري: 50,000 ج.م\n• النمو السنوي: +25%\n• أفضل مشروع: الفيلا السكنية (45% ربح)')}
              padding="p-5"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="text-3xl">📈</div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      صافي الربح
                    </dt>
                    <dd className="text-xl font-bold text-yellow-600">
                      200,000 ج.م
                    </dd>
                    <dd className="text-xs text-yellow-500 mt-1">
                      📊 هامش ربح: 40% | ↗️ +25%
                    </dd>
                  </dl>
                </div>
              </div>
            </EnhancedCard>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <EnhancedCard
              clickable
              className="group hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50 hover:border-blue-200"
              onClick={() => window.location.href = '/accounting'}
            >
              <div className="flex items-center">
                <div className="text-4xl mr-4 group-hover:scale-110 transition-transform duration-200">📊</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-700">المحاسبة العامة</h3>
                  <p className="text-sm text-gray-500 group-hover:text-blue-600">دليل الحسابات والقيود المحاسبية</p>
                  <StatusBadge status="success" className="mt-2">متاح</StatusBadge>
                </div>
                <div className="text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  ←
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="group hover:bg-gradient-to-br hover:from-orange-50 hover:to-yellow-50 hover:border-orange-200"
              onClick={() => window.location.href = '/projects'}
            >
              <div className="flex items-center">
                <div className="text-4xl mr-4 group-hover:scale-110 transition-transform duration-200">🏗️</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-orange-700">إدارة المشاريع</h3>
                  <p className="text-sm text-gray-500 group-hover:text-orange-600">تسجيل ومتابعة المشاريع</p>
                  <StatusBadge status="success" className="mt-2">متاح</StatusBadge>
                </div>
                <div className="text-orange-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  ←
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="group hover:bg-gradient-to-br hover:from-green-50 hover:to-emerald-50 hover:border-green-200"
              onClick={() => window.location.href = '/inventory'}
            >
              <div className="flex items-center">
                <div className="text-4xl mr-4 group-hover:scale-110 transition-transform duration-200">📦</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-green-700">المخزون</h3>
                  <p className="text-sm text-gray-500 group-hover:text-green-600">إدارة المواد والأصناف</p>
                  <StatusBadge status="success" className="mt-2">متاح</StatusBadge>
                </div>
                <div className="text-green-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  ←
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="group hover:bg-gradient-to-br hover:from-orange-50 hover:to-red-50 hover:border-orange-200"
              onClick={() => {
                const invoiceData = {
                  totalInvoices: 156,
                  salesInvoices: 98,
                  purchaseInvoices: 58,
                  totalAmount: 2450000,
                  paidAmount: 1980000,
                  pendingAmount: 470000
                }

                alert(`🧾 نظام الفواتير\n\n` +
                      `إجمالي الفواتير: ${invoiceData.totalInvoices}\n` +
                      `فواتير المبيعات: ${invoiceData.salesInvoices}\n` +
                      `فواتير المشتريات: ${invoiceData.purchaseInvoices}\n` +
                      `إجمالي القيمة: ${invoiceData.totalAmount.toLocaleString('ar-EG')} ج.م\n` +
                      `المبالغ المحصلة: ${invoiceData.paidAmount.toLocaleString('ar-EG')} ج.م\n` +
                      `المبالغ المعلقة: ${invoiceData.pendingAmount.toLocaleString('ar-EG')} ج.م\n\n` +
                      `الميزات المتاحة:\n• إنشاء فواتير المبيعات\n• إدارة فواتير المشتريات\n• تتبع المدفوعات\n• إدارة العملاء والموردين\n• تقارير الفواتير\n\n` +
                      `✅ النظام جاهز للاستخدام!`)
              }}
            >
              <div className="flex items-center">
                <div className="text-4xl mr-4 group-hover:scale-110 transition-transform duration-200">🧾</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-orange-700">الفواتير</h3>
                  <p className="text-sm text-gray-500 group-hover:text-orange-600">فواتير المبيعات والمشتريات</p>
                  <StatusBadge status="success" className="mt-2">متاح</StatusBadge>
                </div>
                <div className="text-orange-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  ←
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="group hover:bg-gradient-to-br hover:from-purple-50 hover:to-pink-50 hover:border-purple-200"
              onClick={() => window.location.href = '/expenses'}
            >
              <div className="flex items-center">
                <div className="text-4xl mr-4 group-hover:scale-110 transition-transform duration-200">💰</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-purple-700">العهد والمصروفات</h3>
                  <p className="text-sm text-gray-500 group-hover:text-purple-600">إدارة العهد والمصروفات العمومية</p>
                  <StatusBadge status="success" className="mt-2">متاح</StatusBadge>
                </div>
                <div className="text-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  ←
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="group hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50 hover:border-blue-200"
              onClick={() => window.location.href = '/admin/users'}
            >
              <div className="flex items-center">
                <div className="text-4xl mr-4 group-hover:scale-110 transition-transform duration-200">👥</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-700">إدارة المستخدمين</h3>
                  <p className="text-sm text-gray-500 group-hover:text-blue-600">المستخدمين والصلاحيات</p>
                  <StatusBadge status="success" className="mt-2">متاح</StatusBadge>
                </div>
                <div className="text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  ←
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard
              clickable
              className="group hover:bg-gradient-to-br hover:from-indigo-50 hover:to-purple-50 hover:border-indigo-200"
              onClick={() => {
                const aiData = {
                  predictedRevenue: 3200000,
                  riskProjects: 2,
                  efficiency: 87,
                  recommendations: 5
                }

                alert(`🤖 التحليلات الذكية\n\n` +
                      `الإيرادات المتوقعة: ${aiData.predictedRevenue.toLocaleString('ar-EG')} ج.م\n` +
                      `المشاريع عالية المخاطر: ${aiData.riskProjects}\n` +
                      `مؤشر الكفاءة: ${aiData.efficiency}%\n` +
                      `التوصيات الذكية: ${aiData.recommendations}\n\n` +
                      `الميزات المتاحة:\n• توقعات الذكاء الاصطناعي\n• تحليل الاتجاهات\n• توصيات ذكية\n• تقارير تنبؤية\n• تحليل المخاطر\n• مؤشرات الأداء الذكية\n\n` +
                      `✅ النظام جاهز للاستخدام!`)
              }}
            >
              <div className="flex items-center">
                <div className="text-4xl mr-4 group-hover:scale-110 transition-transform duration-200">🤖</div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-indigo-700">التحليلات الذكية</h3>
                  <p className="text-sm text-gray-500 group-hover:text-indigo-600">توقعات وتوصيات AI</p>
                  <StatusBadge status="success" className="mt-2">متاح</StatusBadge>
                </div>
                <div className="text-indigo-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  ←
                </div>
              </div>
            </EnhancedCard>

            <EnhancedCard className="hover:bg-gray-50 hover:border-gray-300">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="text-4xl mr-4">⚙️</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">إعدادات النظام</h3>
                    <p className="text-sm text-gray-500">تخصيص وإدارة النظام</p>
                  </div>
                </div>
                <EnhancedButton
                  variant="primary"
                  icon="⚙️"
                  onClick={() => window.location.href = '/settings'}
                >
                  فتح الإعدادات
                </EnhancedButton>
              </div>
            </EnhancedCard>

            <EnhancedCard className="hover:bg-purple-50 hover:border-purple-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="text-4xl mr-4">🚀</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">الميزات المتقدمة</h3>
                    <p className="text-sm text-gray-500">إضافات وميزات متطورة</p>
                  </div>
                </div>
                <EnhancedButton
                  variant="primary"
                  icon="🚀"
                  className="bg-purple-600 hover:bg-purple-700"
                  onClick={() => window.location.href = '/advanced-features'}
                >
                  استكشاف الميزات
                </EnhancedButton>
              </div>
            </EnhancedCard>

            <EnhancedCard className="hover:bg-green-50 hover:border-green-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="text-4xl mr-4">📱</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">تطبيق الهاتف</h3>
                    <p className="text-sm text-gray-500">تثبيت التطبيق على الهاتف</p>
                  </div>
                </div>
                <EnhancedButton
                  variant="success"
                  icon="📱"
                  onClick={() => window.location.href = '/mobile-app'}
                >
                  تثبيت التطبيق
                </EnhancedButton>
              </div>
            </EnhancedCard>
          </div>
        </div>
      </main>
    </div>
  )
}

export default SimpleDashboard
