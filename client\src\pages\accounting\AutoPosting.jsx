import { useState } from 'react'

function AutoPosting() {
  const [activeTab, setActiveTab] = useState('templates')
  const [selectedTemplate, setSelectedTemplate] = useState(null)

  // قوالب القيود المحددة مسبقاً
  const journalTemplates = [
    {
      id: 'SALES_INVOICE',
      name: 'فاتورة مبيعات',
      description: 'قيد تلقائي عند إنشاء فاتورة مبيعات',
      module: 'المبيعات',
      entries: [
        { account: '1130', accountName: 'حسابات العملاء', debitFormula: 'TOTAL_AMOUNT', creditFormula: '0' },
        { account: '4100', accountName: 'إيرادات المبيعات', debitFormula: '0', creditFormula: 'NET_AMOUNT' },
        { account: '2140', accountName: 'ضرائب مستحقة', debitFormula: '0', creditFormula: 'TAX_AMOUNT' }
      ],
      isActive: true,
      autoApprove: false
    },
    {
      id: 'PURCHASE_INVOICE',
      name: 'فاتورة مشتريات',
      description: 'قيد تلقائي عند إنشاء فاتورة مشتريات',
      module: 'المشتريات',
      entries: [
        { account: '1150', accountName: 'المخزون', debitFormula: 'NET_AMOUNT', creditFormula: '0' },
        { account: '1141', accountName: 'ضرائب مدفوعة مقدماً', debitFormula: 'TAX_AMOUNT', creditFormula: '0' },
        { account: '2110', accountName: 'حسابات الموردين', debitFormula: '0', creditFormula: 'TOTAL_AMOUNT' }
      ],
      isActive: true,
      autoApprove: false
    },
    {
      id: 'SALARY_PAYMENT',
      name: 'دفع رواتب',
      description: 'قيد تلقائي عند دفع الرواتب',
      module: 'الموارد البشرية',
      entries: [
        { account: '5210', accountName: 'رواتب إدارية', debitFormula: 'GROSS_SALARY', creditFormula: '0' },
        { account: '2150', accountName: 'رواتب مستحقة', debitFormula: '0', creditFormula: 'NET_SALARY' },
        { account: '2140', accountName: 'ضرائب مستحقة', debitFormula: '0', creditFormula: 'TAX_DEDUCTION' }
      ],
      isActive: true,
      autoApprove: true
    },
    {
      id: 'PROJECT_EXPENSE',
      name: 'مصروف مشروع',
      description: 'قيد تلقائي عند تسجيل مصروف على مشروع',
      module: 'المشاريع',
      entries: [
        { account: '5110', accountName: 'تكلفة مشاريع مباشرة', debitFormula: 'EXPENSE_AMOUNT', creditFormula: '0' },
        { account: '1120', accountName: 'النقدية بالبنوك', debitFormula: '0', creditFormula: 'EXPENSE_AMOUNT' }
      ],
      isActive: true,
      autoApprove: false
    }
  ]

  // القيود المعلقة للمراجعة
  const pendingEntries = [
    {
      id: 'PE-001',
      date: '2024-01-15',
      reference: 'INV-2024-001',
      description: 'فاتورة مبيعات للعميل أحمد علي',
      template: 'فاتورة مبيعات',
      module: 'المبيعات',
      amount: 50000,
      status: 'معلق',
      entries: [
        { account: '1130', accountName: 'حسابات العملاء', debit: 57000, credit: 0 },
        { account: '4100', accountName: 'إيرادات المبيعات', debit: 0, credit: 50000 },
        { account: '2140', accountName: 'ضرائب مستحقة', debit: 0, credit: 7000 }
      ]
    },
    {
      id: 'PE-002',
      date: '2024-01-16',
      reference: 'PUR-2024-005',
      description: 'فاتورة شراء مواد بناء من شركة المواد المتحدة',
      template: 'فاتورة مشتريات',
      module: 'المشتريات',
      amount: 30000,
      status: 'معلق',
      entries: [
        { account: '1150', accountName: 'المخزون', debit: 30000, credit: 0 },
        { account: '1141', accountName: 'ضرائب مدفوعة مقدماً', debit: 4200, credit: 0 },
        { account: '2110', accountName: 'حسابات الموردين', debit: 0, credit: 34200 }
      ]
    },
    {
      id: 'PE-003',
      date: '2024-01-20',
      reference: 'SAL-2024-01',
      description: 'رواتب شهر يناير 2024',
      template: 'دفع رواتب',
      module: 'الموارد البشرية',
      amount: 45000,
      status: 'معلق',
      entries: [
        { account: '5210', accountName: 'رواتب إدارية', debit: 45000, credit: 0 },
        { account: '2150', accountName: 'رواتب مستحقة', debit: 0, credit: 38250 },
        { account: '2140', accountName: 'ضرائب مستحقة', debit: 0, credit: 6750 }
      ]
    }
  ]

  const tabs = [
    { id: 'templates', name: 'قوالب القيود', icon: '📋' },
    { id: 'pending', name: 'القيود المعلقة', icon: '⏳' },
    { id: 'posted', name: 'القيود المرحلة', icon: '✅' },
    { id: 'settings', name: 'إعدادات الترحيل', icon: '⚙️' }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case 'معلق': return 'bg-yellow-100 text-yellow-800'
      case 'مرحل': return 'bg-green-100 text-green-800'
      case 'مرفوض': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleApproveEntry = (entryId) => {
    alert(`✅ تم ترحيل القيد: ${entryId}\n\nسيتم إضافة القيد إلى دفتر الأستاذ العام`)
  }

  const handleRejectEntry = (entryId) => {
    alert(`❌ تم رفض القيد: ${entryId}\n\nسيتم إرجاع القيد للمراجعة`)
  }

  const handleCreateTemplate = () => {
    const templateData = {
      name: prompt('اسم القالب:'),
      description: prompt('وصف القالب:'),
      type: prompt('نوع القالب (expense/revenue/transfer):')
    }

    if (templateData.name && templateData.description && templateData.type) {
      alert(`✅ تم إنشاء القالب بنجاح!\n\nالاسم: ${templateData.name}\nالوصف: ${templateData.description}\nالنوع: ${templateData.type}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                🔄 نظام الترحيل التلقائي
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleCreateTemplate}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                ➕ قالب جديد
              </button>
              <button
                onClick={() => {
                  const reportData = {
                    totalEntries: pendingEntries.length,
                    approvedEntries: pendingEntries.filter(e => e.status === 'مرحل').length,
                    pendingEntries: pendingEntries.filter(e => e.status === 'معلق').length,
                    rejectedEntries: pendingEntries.filter(e => e.status === 'مرفوض').length,
                    totalAmount: pendingEntries.reduce((sum, e) => sum + e.amount, 0)
                  }

                  alert(`📊 تقرير الترحيل التلقائي\n\n` +
                        `إجمالي القيود: ${reportData.totalEntries}\n` +
                        `القيود المرحلة: ${reportData.approvedEntries}\n` +
                        `القيود المعلقة: ${reportData.pendingEntries}\n` +
                        `القيود المرفوضة: ${reportData.rejectedEntries}\n` +
                        `إجمالي المبالغ: ${reportData.totalAmount.toLocaleString('ar-EG')} ج.م\n\n` +
                        `✅ تم إنشاء تقرير الترحيل بنجاح!`)
                }}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📋 تقرير الترحيل
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              
              {/* قوالب القيود */}
              {activeTab === 'templates' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📋 قوالب القيود المحددة مسبقاً</h2>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {journalTemplates.map((template) => (
                      <div 
                        key={template.id} 
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          selectedTemplate?.id === template.id 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedTemplate(template)}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-medium text-gray-900">{template.name}</h3>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              template.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {template.isActive ? 'نشط' : 'معطل'}
                            </span>
                            {template.autoApprove && (
                              <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                ترحيل تلقائي
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                        
                        <div className="text-xs text-gray-500 mb-3">
                          الوحدة: {template.module}
                        </div>
                        
                        <div className="space-y-1">
                          {template.entries.map((entry, index) => (
                            <div key={index} className="flex justify-between text-xs bg-gray-50 p-2 rounded">
                              <span>{entry.account} - {entry.accountName}</span>
                              <span>
                                {entry.debitFormula !== '0' && <span className="text-green-600">مدين: {entry.debitFormula}</span>}
                                {entry.creditFormula !== '0' && <span className="text-red-600">دائن: {entry.creditFormula}</span>}
                              </span>
                            </div>
                          ))}
                        </div>
                        
                        <div className="mt-3 flex justify-end space-x-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              alert(`تعديل القالب: ${template.name}`)
                            }}
                            className="text-blue-600 hover:text-blue-900 text-sm"
                          >
                            تعديل
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              alert(`${template.isActive ? 'تعطيل' : 'تفعيل'} القالب: ${template.name}`)
                            }}
                            className="text-gray-600 hover:text-gray-900 text-sm"
                          >
                            {template.isActive ? 'تعطيل' : 'تفعيل'}
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* القيود المعلقة */}
              {activeTab === 'pending' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">⏳ القيود المعلقة للمراجعة</h2>
                  
                  <div className="space-y-4">
                    {pendingEntries.map((entry) => (
                      <div key={entry.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h3 className="font-medium text-gray-900">{entry.reference}</h3>
                            <p className="text-sm text-gray-600">{entry.description}</p>
                            <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                              <span>التاريخ: {entry.date}</span>
                              <span>الوحدة: {entry.module}</span>
                              <span>القالب: {entry.template}</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(entry.status)}`}>
                              {entry.status}
                            </span>
                            <span className="text-lg font-bold text-gray-900">
                              {entry.amount.toLocaleString('ar-EG')} ج.م
                            </span>
                          </div>
                        </div>
                        
                        <div className="bg-gray-50 rounded-lg p-3 mb-4">
                          <h4 className="font-medium text-gray-700 mb-2">تفاصيل القيد:</h4>
                          <div className="space-y-1">
                            {entry.entries.map((entryDetail, index) => (
                              <div key={index} className="flex justify-between text-sm">
                                <span>{entryDetail.account} - {entryDetail.accountName}</span>
                                <div className="space-x-4">
                                  {entryDetail.debit > 0 && (
                                    <span className="text-green-600 font-medium">
                                      مدين: {entryDetail.debit.toLocaleString('ar-EG')}
                                    </span>
                                  )}
                                  {entryDetail.credit > 0 && (
                                    <span className="text-red-600 font-medium">
                                      دائن: {entryDetail.credit.toLocaleString('ar-EG')}
                                    </span>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => handleRejectEntry(entry.id)}
                            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                          >
                            ❌ رفض
                          </button>
                          <button
                            onClick={() => handleApproveEntry(entry.id)}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                          >
                            ✅ ترحيل
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* القيود المرحلة */}
              {activeTab === 'posted' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">✅ القيود المرحلة</h2>
                  
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div className="text-4xl mb-2">✅</div>
                    <h3 className="text-lg font-medium text-green-800 mb-2">تم ترحيل جميع القيود</h3>
                    <p className="text-green-600">
                      جميع القيود المعتمدة تم ترحيلها بنجاح إلى دفتر الأستاذ العام
                    </p>
                  </div>
                </div>
              )}

              {/* إعدادات الترحيل */}
              {activeTab === 'settings' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">⚙️ إعدادات الترحيل التلقائي</h2>
                  
                  <div className="space-y-6">
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-medium text-gray-900 mb-4">الإعدادات العامة</h3>
                      
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">تفعيل الترحيل التلقائي</h4>
                            <p className="text-sm text-gray-500">تفعيل أو تعطيل نظام الترحيل التلقائي</p>
                          </div>
                          <input
                            type="checkbox"
                            defaultChecked
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">المراجعة الإجبارية</h4>
                            <p className="text-sm text-gray-500">طلب مراجعة جميع القيود قبل الترحيل</p>
                          </div>
                          <input
                            type="checkbox"
                            defaultChecked
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">إشعارات الترحيل</h4>
                            <p className="text-sm text-gray-500">إرسال إشعارات عند ترحيل القيود</p>
                          </div>
                          <input
                            type="checkbox"
                            defaultChecked
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-medium text-gray-900 mb-4">صلاحيات الترحيل</h3>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-700">مدير المحاسبة</span>
                          <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                            ترحيل مباشر
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-700">محاسب أول</span>
                          <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                            مراجعة مطلوبة
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-700">محاسب</span>
                          <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                            عرض فقط
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AutoPosting
