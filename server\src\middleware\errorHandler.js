const errorHandler = (err, req, res, next) => {
    console.error('Error:', err);

    // Default error
    let error = {
        success: false,
        message: err.message || 'Internal Server Error',
        statusCode: err.statusCode || 500
    };

    // Validation errors
    if (err.name === 'ValidationError') {
        error.statusCode = 400;
        error.message = 'Validation Error';
        error.details = Object.values(err.errors).map(val => val.message);
    }

    // Database constraint errors
    if (err.code === 'SQLITE_CONSTRAINT') {
        error.statusCode = 400;
        if (err.message.includes('UNIQUE constraint failed')) {
            error.message = 'Duplicate entry. Record already exists.';
        } else if (err.message.includes('FOREIGN KEY constraint failed')) {
            error.message = 'Invalid reference. Related record not found.';
        } else {
            error.message = 'Database constraint violation.';
        }
    }

    // JWT errors
    if (err.name === 'JsonWebTokenError') {
        error.statusCode = 401;
        error.message = 'Invalid token';
    }

    if (err.name === 'TokenExpiredError') {
        error.statusCode = 401;
        error.message = 'Token expired';
    }

    // Cast errors (invalid ObjectId, etc.)
    if (err.name === 'CastError') {
        error.statusCode = 400;
        error.message = 'Invalid ID format';
    }

    // File upload errors
    if (err.code === 'LIMIT_FILE_SIZE') {
        error.statusCode = 400;
        error.message = 'File too large';
    }

    // Don't leak error details in production
    if (process.env.NODE_ENV === 'production' && error.statusCode === 500) {
        error.message = 'Internal Server Error';
        delete error.stack;
    } else if (process.env.NODE_ENV !== 'production') {
        error.stack = err.stack;
    }

    res.status(error.statusCode).json(error);
};

module.exports = errorHandler;
