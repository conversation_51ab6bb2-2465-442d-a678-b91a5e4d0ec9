const express = require('express');
const db = require('../database/connection');

const router = express.Router();

// Get payments
router.get('/', async (req, res) => {
    try {
        const payments = await db.all(`
            SELECT 
                p.*,
                CASE 
                    WHEN p.payment_type = 'receipt' THEN c.customer_name
                    ELSE s.supplier_name
                END as party_name,
                ba.account_name as bank_account_name
            FROM payments p
            LEFT JOIN customers c ON p.customer_id = c.id
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            LEFT JOIN bank_accounts ba ON p.bank_account_id = ba.id
            ORDER BY p.payment_date DESC
            LIMIT 50
        `);

        res.json({
            success: true,
            data: payments
        });
    } catch (error) {
        console.error('Get payments error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
