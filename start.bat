@echo off
echo Starting Construction ERP System...

echo.
echo Creating database...
cd server
node src/database/simple_migrate.js
if %errorlevel% neq 0 (
    echo Database creation failed!
    pause
    exit /b 1
)

echo.
echo Seeding database...
node src/database/simple_seed.js
if %errorlevel% neq 0 (
    echo Database seeding failed!
    pause
    exit /b 1
)

echo.
echo Starting backend server...
start "Backend Server" cmd /k "npm start"

echo.
echo Starting frontend client...
cd ..\client
start "Frontend Client" cmd /k "npm run dev"

echo.
echo System started successfully!
echo Backend: http://localhost:3001
echo Frontend: http://localhost:5173
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
pause
