@echo off
chcp 65001 >nul
title نظام إدارة المقاولات - Construction ERP System
color 0A

echo.
echo ========================================
echo    🏗️ نظام إدارة المقاولات
echo    Construction ERP System
echo ========================================
echo.

echo 📋 التحقق من متطلبات النظام...

:: Try to find Node.js in common locations
set "NODE_PATH="
set "NPM_PATH="
set "NODE_FOUND=0"

:: Check default installation paths
if exist "C:\Program Files\nodejs\node.exe" (
    set "NODE_PATH=C:\Program Files\nodejs\node.exe"
    set "NPM_PATH=C:\Program Files\nodejs\npm.cmd"
    set "NODE_FOUND=1"
    echo ✅ تم العثور على Node.js في: C:\Program Files\nodejs\
) else if exist "C:\Program Files (x86)\nodejs\node.exe" (
    set "NODE_PATH=C:\Program Files (x86)\nodejs\node.exe"
    set "NPM_PATH=C:\Program Files (x86)\nodejs\npm.cmd"
    set "NODE_FOUND=1"
    echo ✅ تم العثور على Node.js في: C:\Program Files (x86)\nodejs\
) else (
    :: Try to use node from PATH
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        set "NODE_PATH=node"
        set "NPM_PATH=npm"
        set "NODE_FOUND=1"
        echo ✅ تم العثور على Node.js في PATH
    )
)

:: If Node.js not found, show installation instructions
if %NODE_FOUND% equ 0 (
    echo.
    echo ❌ Node.js غير مثبت أو غير موجود
    echo.
    echo 📥 لتثبيت Node.js:
    echo    1. اذهب إلى: https://nodejs.org
    echo    2. حمل النسخة LTS (الموصى بها)
    echo    3. قم بتثبيتها مع الإعدادات الافتراضية
    echo    4. أعد تشغيل الكمبيوتر
    echo    5. شغل هذا الملف مرة أخرى
    echo.
    echo أو يمكنك تحميل Node.js مباشرة من هنا:
    start https://nodejs.org/en/download/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت - الإصدار:
"%NODE_PATH%" --version

:: Check if npm is available
"%NPM_PATH%" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)

echo ✅ npm متاح - الإصدار:
"%NPM_PATH%" --version

echo.
echo 📦 التحقق من التبعيات...

:: Install server dependencies if needed
if not exist "server\node_modules" (
    echo 📥 تثبيت تبعيات الخادم...
    cd server
    call "%NPM_PATH%" install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات الخادم
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ تبعيات الخادم مثبتة
)

:: Install client dependencies if needed
if not exist "client\node_modules" (
    echo 📥 تثبيت تبعيات العميل...
    cd client
    call "%NPM_PATH%" install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات العميل
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ تبعيات العميل مثبتة
)

echo.
echo 🗄️ إعداد قاعدة البيانات...

:: Setup database if needed
if not exist "server\database\construction_erp.db" (
    echo 📊 إنشاء قاعدة البيانات...
    cd server
    call "%NODE_PATH%" src/database/simple_migrate.js
    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )

    echo 🌱 إضافة البيانات الأولية...
    call "%NODE_PATH%" src/database/simple_seed.js
    if %errorlevel% neq 0 (
        echo ❌ فشل في إضافة البيانات الأولية
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ قاعدة البيانات موجودة
)

echo.
echo 🚀 تشغيل النظام...

:: Kill any existing processes on the ports
echo 🔄 إيقاف العمليات السابقة...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

:: Start backend server
echo 🔧 تشغيل خادم النظام...
cd server
start "خادم النظام - Backend" cmd /k "echo 🔧 خادم النظام يعمل على المنفذ 3001 && echo. && \"%NPM_PATH%\" start"

:: Wait for server to start
echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak >nul

:: Start frontend client
echo 🌐 تشغيل واجهة النظام...
cd ..\client
start "واجهة النظام - Frontend" cmd /k "echo 🌐 واجهة النظام تعمل على المنفذ 5173 && echo. && \"%NPM_PATH%\" run dev"

:: Wait for client to start
echo ⏳ انتظار تشغيل الواجهة...
timeout /t 8 /nobreak >nul

echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo ========================================
echo.
echo 🌐 روابط الوصول:
echo    الواجهة الأمامية: http://localhost:5173
echo    خادم API:        http://localhost:3001
echo.
echo 🔑 بيانات الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور:   admin123
echo.
echo 📋 الوحدات المتاحة:
echo    • لوحة التحكم والتقارير
echo    • المحاسبة العامة
echo    • إدارة المشاريع والمقاولات
echo    • المخزون وإدارة المواد
echo    • الفواتير والمدفوعات
echo    • الخزينة والبنوك
echo    • الموارد البشرية
echo    • الضرائب المصرية
echo    • التحليلات الذكية
echo.

:: Open browser automatically
echo 🌐 فتح المتصفح...
timeout /t 3 /nobreak >nul
start http://localhost:5173

echo.
echo 💡 نصائح مهمة:
echo    • استخدم Ctrl+C في نوافذ الخادم لإيقاف النظام
echo    • تأكد من تغيير كلمة المرور الافتراضية
echo    • راجع ملف README_AR.md للمزيد من التفاصيل
echo.
echo اضغط أي مفتاح للخروج من هذه النافذة...
pause >nul

cd ..
