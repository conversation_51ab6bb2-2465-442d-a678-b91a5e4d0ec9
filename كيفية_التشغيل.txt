🏗️ نظام إدارة المقاولات - دليل التشغيل
========================================

📋 المتطلبات:
-----------
1. Node.js (النسخة 16 أو أحدث)
   تحميل من: https://nodejs.org

📁 ملفات التشغيل المتاحة:
-----------------------
1. start.bat - الملف الرئيسي (تلقائي)
2. start_simple.bat - تشغيل مبسط
3. start_manual.bat - دليل التشغيل اليدوي

🚀 طريقة التشغيل السريع:
---------------------
1. انقر مرتين على start.bat
2. انتظر حتى يفتح المتصفح تلقائياً
3. استخدم بيانات الدخول:
   - المستخدم: admin
   - كلمة المرور: admin123

🔧 التشغيل اليدوي (إذا لم يعمل التلقائي):
---------------------------------------
1. افتح Command Prompt كمدير
2. انتقل إلى مجلد المشروع
3. شغل الخادم:
   cd server
   npm install
   npm start

4. افتح Command Prompt جديد
5. شغل الواجهة:
   cd client
   npm install
   npm run dev

6. افتح المتصفح واذهب إلى:
   http://localhost:5173

🌐 روابط النظام:
--------------
- الواجهة الأمامية: http://localhost:5173
- خادم API: http://localhost:3001

❗ حل المشاكل الشائعة:
-------------------
1. "Node.js غير مثبت":
   - حمل وثبت Node.js من nodejs.org
   - أعد تشغيل الكمبيوتر

2. "Unable to connect":
   - تأكد من تشغيل الخادم أولاً
   - انتظر 30 ثانية قبل فتح المتصفح

3. "Port already in use":
   - أغلق جميع نوافذ Command Prompt
   - أعد تشغيل الملف

4. مشاكل التبعيات:
   - احذف مجلدات node_modules
   - شغل npm install مرة أخرى

📞 للدعم:
----------
راجع ملف README_AR.md للمزيد من التفاصيل
