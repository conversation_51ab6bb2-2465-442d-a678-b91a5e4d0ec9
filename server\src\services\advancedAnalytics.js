// خدمة التحليلات المتقدمة
const db = require('../database/connection');

class AdvancedAnalyticsService {
    
    // تحليل الاتجاهات المالية
    static async getFinancialTrends(startDate, endDate) {
        try {
            const query = `
                SELECT 
                    DATE(created_at, 'start of month') as month,
                    SUM(CASE WHEN type = 'revenue' THEN amount ELSE 0 END) as revenue,
                    SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expenses,
                    COUNT(DISTINCT project_id) as active_projects
                FROM accounting_entries 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY DATE(created_at, 'start of month')
                ORDER BY month
            `;
            
            const trends = await db.all(query, [startDate, endDate]);
            
            // حساب معدلات النمو
            const trendsWithGrowth = trends.map((trend, index) => {
                if (index === 0) {
                    return { ...trend, revenueGrowth: 0, expenseGrowth: 0 };
                }
                
                const prevTrend = trends[index - 1];
                const revenueGrowth = prevTrend.revenue > 0 
                    ? ((trend.revenue - prevTrend.revenue) / prevTrend.revenue) * 100 
                    : 0;
                const expenseGrowth = prevTrend.expenses > 0 
                    ? ((trend.expenses - prevTrend.expenses) / prevTrend.expenses) * 100 
                    : 0;
                
                return { ...trend, revenueGrowth, expenseGrowth };
            });
            
            return trendsWithGrowth;
        } catch (error) {
            console.error('Error getting financial trends:', error);
            throw error;
        }
    }

    // تحليل أداء المشاريع المتقدم
    static async getProjectPerformanceAnalysis() {
        try {
            const query = `
                SELECT 
                    p.*,
                    COALESCE(SUM(pt.amount), 0) as actual_cost,
                    COALESCE(AVG(pt.amount), 0) as avg_transaction,
                    COUNT(pt.id) as transaction_count,
                    (p.estimated_budget - COALESCE(SUM(pt.amount), 0)) as budget_remaining,
                    CASE 
                        WHEN p.estimated_budget > 0 THEN 
                            (COALESCE(SUM(pt.amount), 0) / p.estimated_budget) * 100
                        ELSE 0 
                    END as budget_utilization,
                    CASE 
                        WHEN p.end_date < date('now') AND p.status != 'completed' THEN 'delayed'
                        WHEN COALESCE(SUM(pt.amount), 0) > p.estimated_budget THEN 'over_budget'
                        WHEN p.completion_percentage >= 90 THEN 'near_completion'
                        ELSE 'on_track'
                    END as performance_status
                FROM projects p
                LEFT JOIN project_transactions pt ON p.id = pt.project_id
                WHERE p.status IN ('active', 'planning', 'completed')
                GROUP BY p.id
                ORDER BY p.created_at DESC
            `;
            
            const projects = await db.all(query);
            
            // تحليل إضافي
            const analysis = {
                projects,
                summary: {
                    totalProjects: projects.length,
                    onTrack: projects.filter(p => p.performance_status === 'on_track').length,
                    delayed: projects.filter(p => p.performance_status === 'delayed').length,
                    overBudget: projects.filter(p => p.performance_status === 'over_budget').length,
                    avgBudgetUtilization: projects.reduce((sum, p) => sum + p.budget_utilization, 0) / projects.length,
                    totalBudget: projects.reduce((sum, p) => sum + p.estimated_budget, 0),
                    totalActualCost: projects.reduce((sum, p) => sum + p.actual_cost, 0)
                }
            };
            
            return analysis;
        } catch (error) {
            console.error('Error getting project performance analysis:', error);
            throw error;
        }
    }

    // تحليل التدفق النقدي المتقدم
    static async getCashFlowAnalysis(startDate, endDate) {
        try {
            const query = `
                SELECT 
                    DATE(created_at) as date,
                    SUM(CASE WHEN type = 'inflow' THEN amount ELSE 0 END) as inflow,
                    SUM(CASE WHEN type = 'outflow' THEN amount ELSE 0 END) as outflow,
                    SUM(CASE WHEN type = 'inflow' THEN amount ELSE -amount END) as net_flow
                FROM cash_flow_entries 
                WHERE created_at BETWEEN ? AND ?
                GROUP BY DATE(created_at)
                ORDER BY date
            `;
            
            const dailyFlow = await db.all(query, [startDate, endDate]);
            
            // حساب الرصيد التراكمي
            let cumulativeBalance = 0;
            const flowWithBalance = dailyFlow.map(day => {
                cumulativeBalance += day.net_flow;
                return { ...day, cumulative_balance: cumulativeBalance };
            });
            
            // تحليل الأنماط
            const analysis = {
                dailyFlow: flowWithBalance,
                patterns: {
                    avgDailyInflow: dailyFlow.reduce((sum, d) => sum + d.inflow, 0) / dailyFlow.length,
                    avgDailyOutflow: dailyFlow.reduce((sum, d) => sum + d.outflow, 0) / dailyFlow.length,
                    maxInflow: Math.max(...dailyFlow.map(d => d.inflow)),
                    maxOutflow: Math.max(...dailyFlow.map(d => d.outflow)),
                    volatility: this.calculateVolatility(dailyFlow.map(d => d.net_flow))
                }
            };
            
            return analysis;
        } catch (error) {
            console.error('Error getting cash flow analysis:', error);
            throw error;
        }
    }

    // تحليل العملاء والموردين
    static async getCustomerSupplierAnalysis() {
        try {
            // تحليل العملاء
            const customerQuery = `
                SELECT 
                    c.id,
                    c.customer_name,
                    COUNT(i.id) as invoice_count,
                    SUM(i.total_amount) as total_revenue,
                    AVG(i.total_amount) as avg_invoice_amount,
                    MAX(i.created_at) as last_invoice_date,
                    SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as paid_amount,
                    SUM(CASE WHEN i.status != 'paid' THEN i.total_amount ELSE 0 END) as outstanding_amount
                FROM customers c
                LEFT JOIN invoices i ON c.id = i.customer_id
                WHERE i.invoice_type = 'sales'
                GROUP BY c.id
                ORDER BY total_revenue DESC
            `;
            
            const customers = await db.all(customerQuery);
            
            // تحليل الموردين
            const supplierQuery = `
                SELECT 
                    s.id,
                    s.supplier_name,
                    COUNT(i.id) as invoice_count,
                    SUM(i.total_amount) as total_purchases,
                    AVG(i.total_amount) as avg_invoice_amount,
                    MAX(i.created_at) as last_invoice_date,
                    SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as paid_amount,
                    SUM(CASE WHEN i.status != 'paid' THEN i.total_amount ELSE 0 END) as outstanding_amount
                FROM suppliers s
                LEFT JOIN invoices i ON s.id = i.supplier_id
                WHERE i.invoice_type = 'purchase'
                GROUP BY s.id
                ORDER BY total_purchases DESC
            `;
            
            const suppliers = await db.all(supplierQuery);
            
            return {
                customers: {
                    data: customers,
                    topCustomers: customers.slice(0, 10),
                    totalRevenue: customers.reduce((sum, c) => sum + (c.total_revenue || 0), 0),
                    totalOutstanding: customers.reduce((sum, c) => sum + (c.outstanding_amount || 0), 0)
                },
                suppliers: {
                    data: suppliers,
                    topSuppliers: suppliers.slice(0, 10),
                    totalPurchases: suppliers.reduce((sum, s) => sum + (s.total_purchases || 0), 0),
                    totalOutstanding: suppliers.reduce((sum, s) => sum + (s.outstanding_amount || 0), 0)
                }
            };
        } catch (error) {
            console.error('Error getting customer supplier analysis:', error);
            throw error;
        }
    }

    // تحليل المخزون المتقدم
    static async getInventoryAnalysis() {
        try {
            const query = `
                SELECT 
                    i.*,
                    COALESCE(SUM(im.quantity_in), 0) as total_received,
                    COALESCE(SUM(im.quantity_out), 0) as total_issued,
                    COALESCE(AVG(im.unit_cost), 0) as avg_cost,
                    COUNT(im.id) as movement_count,
                    MAX(im.created_at) as last_movement_date,
                    CASE 
                        WHEN i.current_stock <= i.minimum_stock THEN 'low_stock'
                        WHEN i.current_stock >= i.maximum_stock THEN 'overstock'
                        ELSE 'normal'
                    END as stock_status,
                    CASE 
                        WHEN COALESCE(SUM(im.quantity_out), 0) > 0 THEN 
                            COALESCE(SUM(im.quantity_in), 0) / COALESCE(SUM(im.quantity_out), 1)
                        ELSE 0 
                    END as turnover_ratio
                FROM inventory_items i
                LEFT JOIN inventory_movements im ON i.id = im.item_id
                WHERE i.is_active = 1
                GROUP BY i.id
                ORDER BY i.item_name
            `;
            
            const items = await db.all(query);
            
            const analysis = {
                items,
                summary: {
                    totalItems: items.length,
                    lowStock: items.filter(i => i.stock_status === 'low_stock').length,
                    overstock: items.filter(i => i.stock_status === 'overstock').length,
                    totalValue: items.reduce((sum, i) => sum + (i.current_stock * i.avg_cost), 0),
                    avgTurnover: items.reduce((sum, i) => sum + i.turnover_ratio, 0) / items.length,
                    slowMoving: items.filter(i => i.turnover_ratio < 2).length,
                    fastMoving: items.filter(i => i.turnover_ratio > 6).length
                }
            };
            
            return analysis;
        } catch (error) {
            console.error('Error getting inventory analysis:', error);
            throw error;
        }
    }

    // تحليل الربحية
    static async getProfitabilityAnalysis(startDate, endDate) {
        try {
            // تحليل الربحية حسب المشروع
            const projectProfitQuery = `
                SELECT 
                    p.id,
                    p.project_name,
                    p.project_type,
                    COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 0) as revenue,
                    COALESCE(SUM(CASE WHEN ae.type = 'expense' THEN ae.amount ELSE 0 END), 0) as expenses,
                    COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 0) - 
                    COALESCE(SUM(CASE WHEN ae.type = 'expense' THEN ae.amount ELSE 0 END), 0) as profit,
                    CASE 
                        WHEN COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 0) > 0 THEN
                            ((COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 0) - 
                              COALESCE(SUM(CASE WHEN ae.type = 'expense' THEN ae.amount ELSE 0 END), 0)) / 
                             COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 1)) * 100
                        ELSE 0 
                    END as profit_margin
                FROM projects p
                LEFT JOIN accounting_entries ae ON p.id = ae.project_id
                WHERE ae.created_at BETWEEN ? AND ?
                GROUP BY p.id
                HAVING revenue > 0
                ORDER BY profit DESC
            `;
            
            const projectProfits = await db.all(projectProfitQuery, [startDate, endDate]);
            
            // تحليل الربحية حسب نوع المشروع
            const typeProfitQuery = `
                SELECT 
                    p.project_type,
                    COUNT(DISTINCT p.id) as project_count,
                    COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 0) as total_revenue,
                    COALESCE(SUM(CASE WHEN ae.type = 'expense' THEN ae.amount ELSE 0 END), 0) as total_expenses,
                    COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 0) - 
                    COALESCE(SUM(CASE WHEN ae.type = 'expense' THEN ae.amount ELSE 0 END), 0) as total_profit,
                    AVG(CASE 
                        WHEN COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 0) > 0 THEN
                            ((COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 0) - 
                              COALESCE(SUM(CASE WHEN ae.type = 'expense' THEN ae.amount ELSE 0 END), 0)) / 
                             COALESCE(SUM(CASE WHEN ae.type = 'revenue' THEN ae.amount ELSE 0 END), 1)) * 100
                        ELSE 0 
                    END) as avg_profit_margin
                FROM projects p
                LEFT JOIN accounting_entries ae ON p.id = ae.project_id
                WHERE ae.created_at BETWEEN ? AND ?
                GROUP BY p.project_type
                ORDER BY total_profit DESC
            `;
            
            const typeProfits = await db.all(typeProfitQuery, [startDate, endDate]);
            
            return {
                projectProfitability: projectProfits,
                typeProfitability: typeProfits,
                summary: {
                    totalProjects: projectProfits.length,
                    profitableProjects: projectProfits.filter(p => p.profit > 0).length,
                    totalRevenue: projectProfits.reduce((sum, p) => sum + p.revenue, 0),
                    totalExpenses: projectProfits.reduce((sum, p) => sum + p.expenses, 0),
                    totalProfit: projectProfits.reduce((sum, p) => sum + p.profit, 0),
                    avgProfitMargin: projectProfits.reduce((sum, p) => sum + p.profit_margin, 0) / projectProfits.length
                }
            };
        } catch (error) {
            console.error('Error getting profitability analysis:', error);
            throw error;
        }
    }

    // حساب التقلبات (Volatility)
    static calculateVolatility(values) {
        if (values.length < 2) return 0;
        
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        return Math.sqrt(variance);
    }

    // تحليل شامل للوحة التحكم
    static async getComprehensiveDashboard(startDate, endDate) {
        try {
            const [
                financialTrends,
                projectAnalysis,
                cashFlowAnalysis,
                customerSupplierAnalysis,
                inventoryAnalysis,
                profitabilityAnalysis
            ] = await Promise.all([
                this.getFinancialTrends(startDate, endDate),
                this.getProjectPerformanceAnalysis(),
                this.getCashFlowAnalysis(startDate, endDate),
                this.getCustomerSupplierAnalysis(),
                this.getInventoryAnalysis(),
                this.getProfitabilityAnalysis(startDate, endDate)
            ]);
            
            return {
                financialTrends,
                projectAnalysis,
                cashFlowAnalysis,
                customerSupplierAnalysis,
                inventoryAnalysis,
                profitabilityAnalysis,
                generatedAt: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error getting comprehensive dashboard:', error);
            throw error;
        }
    }
}

module.exports = AdvancedAnalyticsService;
