import { useState } from 'react'
import { Enhanced<PERSON>ard, Enhanced<PERSON><PERSON>on, StatusBadge } from '../../components/ui'

function UserManagement() {
  const [activeTab, setActiveTab] = useState('users')
  const [showNewUserModal, setShowNewUserModal] = useState(false)
  const [showPermissionsModal, setShowPermissionsModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState(null)
  const [isLoading, setIsLoading] = useState(false)

  // بيانات المستخدمين
  const [users, setUsers] = useState([
    {
      id: 'USR-001',
      username: 'admin',
      firstName: 'مدير',
      lastName: 'النظام',
      email: '<EMAIL>',
      phone: '***********',
      role: 'مدير عام',
      department: 'الإدارة',
      status: 'نشط',
      lastLogin: '2024-01-20 10:30',
      permissions: ['all']
    },
    {
      id: 'USR-002',
      username: 'accountant',
      firstName: 'أحمد',
      lastName: 'محمد',
      email: '<EMAIL>',
      phone: '***********',
      role: 'محاسب',
      department: 'المحاسبة',
      status: 'نشط',
      lastLogin: '2024-01-20 09:15',
      permissions: ['accounting', 'reports', 'expenses']
    },
    {
      id: 'USR-003',
      username: 'projectmgr',
      firstName: 'سارة',
      lastName: 'أحمد',
      email: '<EMAIL>',
      phone: '***********',
      role: 'مدير مشاريع',
      department: 'المشاريع',
      status: 'نشط',
      lastLogin: '2024-01-19 16:45',
      permissions: ['projects', 'inventory', 'reports']
    },
    {
      id: 'USR-004',
      username: 'hr_manager',
      firstName: 'محمد',
      lastName: 'علي',
      email: '<EMAIL>',
      phone: '***********',
      role: 'مدير موارد بشرية',
      department: 'الموارد البشرية',
      status: 'معطل',
      lastLogin: '2024-01-18 14:20',
      permissions: ['hr', 'reports']
    }
  ])

  // الأدوار والصلاحيات
  const roles = [
    {
      id: 'admin',
      name: 'مدير عام',
      description: 'صلاحيات كاملة على النظام',
      permissions: ['all']
    },
    {
      id: 'accountant',
      name: 'محاسب',
      description: 'إدارة المحاسبة والتقارير المالية',
      permissions: ['accounting', 'reports', 'expenses', 'banking']
    },
    {
      id: 'project_manager',
      name: 'مدير مشاريع',
      description: 'إدارة المشاريع والمخزون',
      permissions: ['projects', 'inventory', 'reports']
    },
    {
      id: 'hr_manager',
      name: 'مدير موارد بشرية',
      description: 'إدارة الموظفين والرواتب',
      permissions: ['hr', 'reports']
    },
    {
      id: 'viewer',
      name: 'مستخدم عادي',
      description: 'عرض التقارير فقط',
      permissions: ['reports']
    }
  ]

  // الصلاحيات المتاحة
  const availablePermissions = [
    { id: 'all', name: 'جميع الصلاحيات', description: 'الوصول الكامل للنظام' },
    { id: 'accounting', name: 'المحاسبة', description: 'إدارة الحسابات والقيود' },
    { id: 'projects', name: 'المشاريع', description: 'إدارة المشاريع والمقاولات' },
    { id: 'inventory', name: 'المخزون', description: 'إدارة المواد والمخزون' },
    { id: 'expenses', name: 'المصروفات', description: 'إدارة العهد والمصروفات' },
    { id: 'banking', name: 'البنوك', description: 'إدارة الحسابات البنكية' },
    { id: 'hr', name: 'الموارد البشرية', description: 'إدارة الموظفين والرواتب' },
    { id: 'reports', name: 'التقارير', description: 'عرض وإنشاء التقارير' },
    { id: 'settings', name: 'الإعدادات', description: 'إعدادات النظام' },
    { id: 'users', name: 'إدارة المستخدمين', description: 'إضافة وتعديل المستخدمين' }
  ]

  const [newUser, setNewUser] = useState({
    username: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: '',
    department: '',
    password: '',
    confirmPassword: '',
    permissions: []
  })

  const handleNewUser = () => {
    setShowNewUserModal(true)
  }

  const handleSaveUser = async () => {
    if (!newUser.username || !newUser.firstName || !newUser.lastName || !newUser.email || !newUser.role) {
      alert('⚠️ يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (newUser.password !== newUser.confirmPassword) {
      alert('⚠️ كلمة المرور وتأكيدها غير متطابقتين')
      return
    }

    if (users.find(u => u.username === newUser.username)) {
      alert('⚠️ اسم المستخدم موجود بالفعل')
      return
    }

    setIsLoading(true)
    
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const selectedRole = roles.find(r => r.id === newUser.role)
      const newUserData = {
        id: `USR-${String(users.length + 1).padStart(3, '0')}`,
        ...newUser,
        status: 'نشط',
        lastLogin: 'لم يسجل دخول بعد',
        permissions: selectedRole ? selectedRole.permissions : []
      }
      
      setUsers([...users, newUserData])
      
      alert(`✅ تم إضافة المستخدم بنجاح!\n\nاسم المستخدم: ${newUser.username}\nالاسم: ${newUser.firstName} ${newUser.lastName}\nالدور: ${selectedRole?.name}\nالقسم: ${newUser.department}`)
      
      setNewUser({
        username: '',
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        role: '',
        department: '',
        password: '',
        confirmPassword: '',
        permissions: []
      })
      
      setShowNewUserModal(false)
    } catch (error) {
      alert('❌ حدث خطأ أثناء إضافة المستخدم')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelUser = () => {
    setShowNewUserModal(false)
    setNewUser({
      username: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      role: '',
      department: '',
      password: '',
      confirmPassword: '',
      permissions: []
    })
  }

  const handleEditPermissions = (user) => {
    setSelectedUser(user)
    setShowPermissionsModal(true)
  }

  const handleToggleUserStatus = (userId) => {
    setUsers(users.map(user => 
      user.id === userId 
        ? { ...user, status: user.status === 'نشط' ? 'معطل' : 'نشط' }
        : user
    ))
    
    const user = users.find(u => u.id === userId)
    alert(`${user.status === 'نشط' ? '🔒 تم تعطيل' : '✅ تم تفعيل'} المستخدم: ${user.firstName} ${user.lastName}`)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'نشط': return 'bg-green-100 text-green-800'
      case 'معطل': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPermissionName = (permissionId) => {
    const permission = availablePermissions.find(p => p.id === permissionId)
    return permission ? permission.name : permissionId
  }

  const tabs = [
    { id: 'users', name: 'المستخدمين', icon: '👥' },
    { id: 'roles', name: 'الأدوار', icon: '🔐' },
    { id: 'permissions', name: 'الصلاحيات', icon: '⚙️' },
    { id: 'activity', name: 'سجل النشاط', icon: '📋' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                👥 إدارة المستخدمين والصلاحيات
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <EnhancedButton
                variant="primary"
                icon="➕"
                onClick={handleNewUser}
                disabled={isLoading}
              >
                مستخدم جديد
              </EnhancedButton>
              <EnhancedButton
                variant="success"
                icon="📊"
                onClick={() => {
                  const reportData = {
                    totalUsers: users.length,
                    activeUsers: users.filter(u => u.status === 'نشط').length,
                    inactiveUsers: users.filter(u => u.status === 'معطل').length,
                    departments: [...new Set(users.map(u => u.department))].length
                  }
                  
                  alert(`📊 تقرير المستخدمين\n\n` +
                        `إجمالي المستخدمين: ${reportData.totalUsers}\n` +
                        `المستخدمين النشطين: ${reportData.activeUsers}\n` +
                        `المستخدمين المعطلين: ${reportData.inactiveUsers}\n` +
                        `عدد الأقسام: ${reportData.departments}\n\n` +
                        `✅ تم إنشاء تقرير المستخدمين بنجاح!`)
                }}
              >
                تقرير المستخدمين
              </EnhancedButton>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">

              {/* المستخدمين */}
              {activeTab === 'users' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">👥 إدارة المستخدمين</h2>

                  {/* إحصائيات سريعة */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <EnhancedCard className="text-center bg-blue-50 border-blue-200">
                      <div className="text-3xl mb-2">👥</div>
                      <div className="text-blue-800 text-sm font-medium">إجمالي المستخدمين</div>
                      <div className="text-blue-900 text-2xl font-bold">
                        {users.length}
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-green-50 border-green-200">
                      <div className="text-3xl mb-2">✅</div>
                      <div className="text-green-800 text-sm font-medium">المستخدمين النشطين</div>
                      <div className="text-green-900 text-2xl font-bold">
                        {users.filter(u => u.status === 'نشط').length}
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-red-50 border-red-200">
                      <div className="text-3xl mb-2">🔒</div>
                      <div className="text-red-800 text-sm font-medium">المستخدمين المعطلين</div>
                      <div className="text-red-900 text-2xl font-bold">
                        {users.filter(u => u.status === 'معطل').length}
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-purple-50 border-purple-200">
                      <div className="text-3xl mb-2">🏢</div>
                      <div className="text-purple-800 text-sm font-medium">الأقسام</div>
                      <div className="text-purple-900 text-2xl font-bold">
                        {[...new Set(users.map(u => u.department))].length}
                      </div>
                    </EnhancedCard>
                  </div>

                  {/* جدول المستخدمين */}
                  <EnhancedCard>
                    <div className="space-y-4">
                      {users.map((user) => (
                        <div key={user.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-4">
                              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <span className="text-blue-600 font-medium">
                                  {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                                </span>
                              </div>
                              <div>
                                <h4 className="font-medium text-gray-900">{user.firstName} {user.lastName}</h4>
                                <p className="text-sm text-gray-600">@{user.username} - {user.role}</p>
                              </div>
                              <StatusBadge status={user.status === 'نشط' ? 'success' : 'error'}>
                                {user.status}
                              </StatusBadge>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-gray-900">{user.department}</div>
                              <div className="text-xs text-gray-500">آخر دخول: {user.lastLogin}</div>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                            <div>
                              <div className="text-sm text-gray-500">البريد الإلكتروني</div>
                              <div className="font-medium">{user.email}</div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-500">رقم الهاتف</div>
                              <div className="font-medium">{user.phone}</div>
                            </div>
                          </div>

                          <div className="mb-3">
                            <div className="text-sm text-gray-500 mb-1">الصلاحيات</div>
                            <div className="flex flex-wrap gap-1">
                              {user.permissions.map((permission) => (
                                <span
                                  key={permission}
                                  className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                                >
                                  {getPermissionName(permission)}
                                </span>
                              ))}
                            </div>
                          </div>

                          <div className="flex justify-end space-x-2">
                            <EnhancedButton
                              size="sm"
                              variant="ghost"
                              onClick={() => handleEditPermissions(user)}
                            >
                              تعديل الصلاحيات
                            </EnhancedButton>
                            <EnhancedButton
                              size="sm"
                              variant={user.status === 'نشط' ? 'warning' : 'success'}
                              onClick={() => handleToggleUserStatus(user.id)}
                            >
                              {user.status === 'نشط' ? 'تعطيل' : 'تفعيل'}
                            </EnhancedButton>
                          </div>
                        </div>
                      ))}
                    </div>
                  </EnhancedCard>
                </div>
              )}

              {/* الأدوار */}
              {activeTab === 'roles' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">🔐 إدارة الأدوار</h2>

                  <EnhancedCard>
                    <div className="space-y-4">
                      {roles.map((role) => (
                        <div key={role.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div>
                              <h4 className="font-medium text-gray-900">{role.name}</h4>
                              <p className="text-sm text-gray-600">{role.description}</p>
                            </div>
                            <div className="text-sm text-gray-500">
                              {users.filter(u => u.role === role.name).length} مستخدم
                            </div>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {role.permissions.map((permission) => (
                              <span
                                key={permission}
                                className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                              >
                                {getPermissionName(permission)}
                              </span>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </EnhancedCard>
                </div>
              )}

              {/* الصلاحيات */}
              {activeTab === 'permissions' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">⚙️ الصلاحيات المتاحة</h2>

                  <EnhancedCard>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {availablePermissions.map((permission) => (
                        <div key={permission.id} className="border border-gray-200 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-1">{permission.name}</h4>
                          <p className="text-sm text-gray-600">{permission.description}</p>
                        </div>
                      ))}
                    </div>
                  </EnhancedCard>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>

      {/* نموذج إضافة مستخدم جديد */}
      {showNewUserModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">➕ إضافة مستخدم جديد</h2>
                <button
                  onClick={handleCancelUser}
                  className="text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                {/* معلومات أساسية */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      اسم المستخدم *
                    </label>
                    <input
                      type="text"
                      value={newUser.username}
                      onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="ahmed_mohamed"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الدور *
                    </label>
                    <select
                      value={newUser.role}
                      onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    >
                      <option value="">اختر الدور</option>
                      {roles.map((role) => (
                        <option key={role.id} value={role.id}>{role.name}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الاسم الأول *
                    </label>
                    <input
                      type="text"
                      value={newUser.firstName}
                      onChange={(e) => setNewUser({...newUser, firstName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="أحمد"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      اسم العائلة *
                    </label>
                    <input
                      type="text"
                      value={newUser.lastName}
                      onChange={(e) => setNewUser({...newUser, lastName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="محمد"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="<EMAIL>"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      رقم الهاتف
                    </label>
                    <input
                      type="text"
                      value={newUser.phone}
                      onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="***********"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    القسم
                  </label>
                  <input
                    type="text"
                    value={newUser.department}
                    onChange={(e) => setNewUser({...newUser, department: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="المحاسبة"
                    disabled={isLoading}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      كلمة المرور *
                    </label>
                    <input
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="••••••••"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      تأكيد كلمة المرور *
                    </label>
                    <input
                      type="password"
                      value={newUser.confirmPassword}
                      onChange={(e) => setNewUser({...newUser, confirmPassword: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="••••••••"
                      disabled={isLoading}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
                <EnhancedButton
                  variant="ghost"
                  onClick={handleCancelUser}
                  disabled={isLoading}
                >
                  إلغاء
                </EnhancedButton>
                <EnhancedButton
                  variant="primary"
                  onClick={handleSaveUser}
                  disabled={isLoading}
                  icon={isLoading ? "⏳" : "💾"}
                >
                  {isLoading ? 'جاري الحفظ...' : 'حفظ المستخدم'}
                </EnhancedButton>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserManagement
