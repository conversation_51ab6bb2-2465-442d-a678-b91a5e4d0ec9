import { useMemo } from 'react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { formatCurrency } from '../../utils/formatters'

// مخطط خطي للإيرادات والمصروفات
export function RevenueExpenseChart({ data, height = 300 }) {
  const chartData = useMemo(() => {
    return data?.map(item => ({
      ...item,
      month: new Date(item.date).toLocaleDateString('ar-EG', { month: 'short' })
    })) || []
  }, [data])

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" />
        <YAxis tickFormatter={(value) => formatCurrency(value)} />
        <Tooltip 
          formatter={(value) => [formatCurrency(value), '']}
          labelFormatter={(label) => `الشهر: ${label}`}
        />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="revenue" 
          stroke="#10B981" 
          strokeWidth={3}
          name="الإيرادات"
        />
        <Line 
          type="monotone" 
          dataKey="expenses" 
          stroke="#EF4444" 
          strokeWidth={3}
          name="المصروفات"
        />
      </LineChart>
    </ResponsiveContainer>
  )
}

// مخطط منطقة للتدفق النقدي
export function CashFlowChart({ data, height = 300 }) {
  const chartData = useMemo(() => {
    return data?.map(item => ({
      ...item,
      date: new Date(item.date).toLocaleDateString('ar-EG', { month: 'short', day: 'numeric' }),
      netFlow: item.inflow - item.outflow
    })) || []
  }, [data])

  return (
    <ResponsiveContainer width="100%" height={height}>
      <AreaChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="date" />
        <YAxis tickFormatter={(value) => formatCurrency(value)} />
        <Tooltip 
          formatter={(value) => [formatCurrency(value), '']}
          labelFormatter={(label) => `التاريخ: ${label}`}
        />
        <Legend />
        <Area 
          type="monotone" 
          dataKey="inflow" 
          stackId="1"
          stroke="#10B981" 
          fill="#10B981"
          fillOpacity={0.6}
          name="التدفق الداخل"
        />
        <Area 
          type="monotone" 
          dataKey="outflow" 
          stackId="2"
          stroke="#EF4444" 
          fill="#EF4444"
          fillOpacity={0.6}
          name="التدفق الخارج"
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}

// مخطط أعمدة لأداء المشاريع
export function ProjectPerformanceChart({ data, height = 300 }) {
  const chartData = useMemo(() => {
    return data?.map(project => ({
      ...project,
      name: project.project_name?.substring(0, 15) + (project.project_name?.length > 15 ? '...' : ''),
      budgetVariance: project.actual_cost - project.estimated_budget,
      completionRate: project.completion_percentage || 0
    })) || []
  }, [data])

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis tickFormatter={(value) => formatCurrency(value)} />
        <Tooltip 
          formatter={(value, name) => [
            name === 'completionRate' ? `${value}%` : formatCurrency(value), 
            name === 'estimated_budget' ? 'الميزانية المقدرة' :
            name === 'actual_cost' ? 'التكلفة الفعلية' :
            name === 'budgetVariance' ? 'انحراف الميزانية' :
            name === 'completionRate' ? 'نسبة الإنجاز' : ''
          ]}
          labelFormatter={(label) => `المشروع: ${label}`}
        />
        <Legend />
        <Bar dataKey="estimated_budget" fill="#3B82F6" name="الميزانية المقدرة" />
        <Bar dataKey="actual_cost" fill="#EF4444" name="التكلفة الفعلية" />
      </BarChart>
    </ResponsiveContainer>
  )
}

// مخطط دائري لتوزيع المصروفات
export function ExpenseDistributionChart({ data, height = 300 }) {
  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']
  
  const chartData = useMemo(() => {
    return data?.map((item, index) => ({
      ...item,
      fill: COLORS[index % COLORS.length]
    })) || []
  }, [data])

  return (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="amount"
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.fill} />
          ))}
        </Pie>
        <Tooltip formatter={(value) => [formatCurrency(value), 'المبلغ']} />
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  )
}

// مخطط مؤشرات الأداء الرئيسية
export function KPIChart({ data, height = 200 }) {
  const kpiData = useMemo(() => {
    if (!data) return []
    
    return [
      {
        name: 'الربحية',
        value: data.profitMargin || 0,
        target: 20,
        unit: '%'
      },
      {
        name: 'السيولة',
        value: data.liquidityRatio || 0,
        target: 1.5,
        unit: 'x'
      },
      {
        name: 'دوران المخزون',
        value: data.inventoryTurnover || 0,
        target: 6,
        unit: 'x'
      },
      {
        name: 'متوسط التحصيل',
        value: data.averageCollectionDays || 0,
        target: 30,
        unit: 'يوم'
      }
    ]
  }, [data])

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={kpiData} layout="horizontal">
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis type="number" />
        <YAxis dataKey="name" type="category" width={80} />
        <Tooltip 
          formatter={(value, name) => [
            `${value}${kpiData.find(item => item.name === name)?.unit || ''}`,
            name === 'value' ? 'القيمة الحالية' : 'الهدف'
          ]}
        />
        <Legend />
        <Bar dataKey="value" fill="#3B82F6" name="القيمة الحالية" />
        <Bar dataKey="target" fill="#10B981" name="الهدف" />
      </BarChart>
    </ResponsiveContainer>
  )
}

// مخطط الاتجاهات الشهرية
export function MonthlyTrendsChart({ data, height = 300 }) {
  const chartData = useMemo(() => {
    return data?.map(item => ({
      ...item,
      month: new Date(item.date).toLocaleDateString('ar-EG', { month: 'long' }),
      profitMargin: item.revenue > 0 ? ((item.revenue - item.expenses) / item.revenue * 100) : 0
    })) || []
  }, [data])

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" />
        <YAxis yAxisId="left" tickFormatter={(value) => formatCurrency(value)} />
        <YAxis yAxisId="right" orientation="right" tickFormatter={(value) => `${value}%`} />
        <Tooltip 
          formatter={(value, name) => [
            name === 'profitMargin' ? `${value.toFixed(1)}%` : formatCurrency(value),
            name === 'revenue' ? 'الإيرادات' :
            name === 'expenses' ? 'المصروفات' :
            name === 'profitMargin' ? 'هامش الربح' : ''
          ]}
          labelFormatter={(label) => `الشهر: ${label}`}
        />
        <Legend />
        <Bar yAxisId="left" dataKey="revenue" fill="#10B981" name="الإيرادات" />
        <Bar yAxisId="left" dataKey="expenses" fill="#EF4444" name="المصروفات" />
        <Line 
          yAxisId="right" 
          type="monotone" 
          dataKey="profitMargin" 
          stroke="#F59E0B" 
          strokeWidth={3}
          name="هامش الربح %"
        />
      </LineChart>
    </ResponsiveContainer>
  )
}
