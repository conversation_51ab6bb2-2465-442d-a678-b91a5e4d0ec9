# 🏗️ نظام إدارة المقاولات

نظام محاسبي شامل للمقاولات مع الامتثال الضريبي المصري والذكاء الاصطناعي

## ✨ الميزات الرئيسية

### 📊 الوحدات الأساسية
- **المحاسبة العامة**: دليل الحسابات، دفتر الأستاذ، ميزان المراجعة، قائمة الدخل
- **الخزينة والبنوك**: معاملات نقدية/بنكية، مقبوضات/مدفوعات مع توقع التدفق النقدي
- **الحسابات المدينة/الدائنة**: الفوترة، المدفوعات، تقارير الأعمار
- **إدارة المخزون**: الاستلام، الصرف، تنبيهات المخزون مع تحليل الاستهلاك
- **إدارة المشاريع**: تسجيل المشاريع، تتبع التكاليف مع التحليل التنبؤي
- **الموارد البشرية**: الحضور، الرواتب، السلف، العهد
- **الضرائب والامتثال**: ضريبة القيمة المضافة، ضريبة الدخل، الخصم والإضافة، نموذج 41
- **التحليلات الذكية**: مراقبة الأداء، تحليل المخاطر، التوصيات

### 🛠️ التقنيات المستخدمة
- **الواجهة الأمامية**: React 18 + Vite + Tailwind CSS
- **الخادم**: Node.js + Express + SQLite
- **مكونات الذكاء الاصطناعي**: تكامل Python للتحليلات
- **المصادقة**: JWT مع التحكم في الأدوار
- **التصدير**: إنتاج PDF/Excel

## 🚀 البدء السريع

### الطريقة التلقائية (Windows)
```bash
# تشغيل الملف التلقائي
start.bat
```

### الطريقة اليدوية

1. تثبيت التبعيات:
```bash
npm run install:all
```

2. تشغيل خوادم التطوير:
```bash
npm run dev
```

3. الوصول للتطبيق:
- الواجهة الأمامية: http://localhost:5173
- API الخادم: http://localhost:3001

## 🔑 بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

## 📁 هيكل المشروع

```
construction-erp/
├── client/                 # واجهة React
│   ├── src/
│   │   ├── components/     # المكونات القابلة لإعادة الاستخدام
│   │   ├── pages/          # مكونات الصفحات
│   │   ├── services/       # خدمات API
│   │   ├── store/          # إدارة الحالة
│   │   └── utils/          # الأدوات المساعدة
├── server/                 # خادم Node.js
│   ├── src/
│   │   ├── routes/         # مسارات API
│   │   ├── services/       # خدمات الأعمال
│   │   ├── database/       # قاعدة البيانات
│   │   └── middleware/     # الوسطاء
└── docs/                   # الوثائق
```

## 🎯 الوحدات المتاحة

### 1. لوحة التحكم
- نظرة عامة على النظام
- إحصائيات سريعة
- التوصيات الذكية

### 2. المحاسبة العامة
- دليل الحسابات
- دفتر الأستاذ العام
- ميزان المراجعة

### 3. إدارة المشاريع
- قائمة المشاريع
- تتبع التكاليف
- تقارير الأداء

### 4. المخزون
- إدارة الأصناف
- حركات المخزون
- تقارير المخزون

### 5. الفواتير
- فواتير المبيعات
- فواتير المشتريات
- تتبع الحالة

### 6. المدفوعات
- المقبوضات والمدفوعات
- ربط الفواتير
- تقارير التدفق النقدي

### 7. الخزينة والبنوك
- الحسابات البنكية
- الحركات البنكية
- المطابقة البنكية

### 8. الموارد البشرية
- سجلات الموظفين
- الحضور والانصراف
- الرواتب والمستحقات

### 9. الضرائب
- إعدادات الضرائب المصرية
- تقارير ضريبة القيمة المضافة
- نموذج 41

### 10. التقارير
- التقارير المالية
- تقارير المشاريع
- تقارير الضرائب

### 11. التحليلات الذكية
- التوقعات المالية
- تحليل المخاطر
- التوصيات الذكية

### 12. الإعدادات
- إعدادات النظام
- إدارة المستخدمين
- الأمان

## 🇪🇬 الامتثال المصري

### ضريبة القيمة المضافة
- معدل 14% حسب القانون المصري
- تقارير شهرية تلقائية
- نموذج 41 الإلكتروني

### ضريبة الخصم والإضافة
- معدل 10% للموردين
- شهادات الخصم التلقائية
- تقارير الامتثال

### ضريبة الدخل
- حساب تلقائي للشرائح
- تقارير سنوية
- امتثال كامل للقانون

## 🤖 الذكاء الاصطناعي

### التوقعات المالية
- توقع التدفق النقدي
- تحليل تجاوز التكاليف
- توقع تأخير المدفوعات

### التحليلات الذكية
- تحليل أداء المشاريع
- كشف المخاطر المبكر
- توصيات التحسين

### التحسين التلقائي
- تحسين إدارة المخزون
- تحسين كفاءة المشاريع
- تقليل التكاليف

## 📞 الدعم

للحصول على الدعم:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +20-123-456-7890

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT

---

🇪🇬 **صنع في مصر بحب** ❤️
