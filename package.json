{"name": "construction-erp", "version": "1.0.0", "description": "نظام محاسبي شامل للمقاولات مع الامتثال الضريبي المصري والذكاء الاصطناعي", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test && cd ../client && npm test"}, "keywords": ["construction", "accounting", "erp", "egyptian-tax", "ai-analytics"], "author": "Construction ERP Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}