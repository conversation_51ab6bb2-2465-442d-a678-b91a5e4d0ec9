const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        message: 'Construction ERP Server is running',
        timestamp: new Date().toISOString()
    });
});

// Simple login endpoint
app.post('/api/auth/login', async (req, res) => {
    const { username, password } = req.body;
    
    console.log('Login attempt:', { username, password });
    
    // Simple check for admin user
    if (username === 'admin' && password === 'admin123') {
        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: {
                    id: 1,
                    username: 'admin',
                    firstName: 'مدير',
                    lastName: 'النظام',
                    role: 'admin',
                    email: '<EMAIL>'
                },
                token: 'test-token-123'
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: 'Invalid credentials'
        });
    }
});

// Simple dashboard data
app.get('/api/reports/dashboard', (req, res) => {
    res.json({
        success: true,
        data: {
            totalRevenue: 500000,
            totalExpenses: 300000,
            netProfit: 200000,
            activeProjects: 5,
            recentTransactions: [
                {
                    id: 1,
                    description: 'إيرادات مشروع الفيلا',
                    amount: 250000,
                    date: new Date().toISOString()
                }
            ]
        }
    });
});

// Catch all other routes
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'API endpoint not found',
        path: req.originalUrl
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Test Construction ERP Server running on port', PORT);
    console.log('📊 Health check: http://localhost:' + PORT + '/health');
    console.log('🔗 API Base URL: http://localhost:' + PORT + '/api');
    console.log('🔑 Login: admin / admin123');
});

module.exports = app;
