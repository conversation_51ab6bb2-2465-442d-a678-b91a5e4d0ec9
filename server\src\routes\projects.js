const express = require('express');
const { body, validationResult } = require('express-validator');
const db = require('../database/connection');
const { authorize } = require('../middleware/auth');

const router = express.Router();

// Get all projects
router.get('/', async (req, res) => {
    try {
        const { status, page = 1, limit = 20 } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = '1=1';
        let params = [];

        if (status) {
            whereClause += ' AND p.status = ?';
            params.push(status);
        }

        const projects = await db.all(`
            SELECT 
                p.*,
                u.first_name || ' ' || u.last_name as project_manager_name,
                COUNT(pt.id) as transaction_count,
                COALESCE(SUM(pt.amount), 0) as actual_cost
            FROM projects p
            LEFT JOIN users u ON p.project_manager_id = u.id
            LEFT JOIN project_transactions pt ON p.id = pt.project_id
            WHERE ${whereClause}
            GROUP BY p.id
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, limit, offset]);

        res.json({
            success: true,
            data: projects
        });

    } catch (error) {
        console.error('Get projects error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Create project
router.post('/', authorize('admin', 'project_manager'), [
    body('projectCode').notEmpty().withMessage('Project code is required'),
    body('projectName').notEmpty().withMessage('Project name is required'),
    body('clientName').notEmpty().withMessage('Client name is required'),
    body('projectType').isIn(['civil', 'structural', 'finishing', 'electrical', 'plumbing', 'infrastructure'])
        .withMessage('Invalid project type')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            projectCode, projectName, clientName, clientContact, projectType,
            startDate, endDate, estimatedBudget, projectManagerId, description
        } = req.body;

        const result = await db.run(`
            INSERT INTO projects (
                project_code, project_name, client_name, client_contact,
                project_type, start_date, end_date, estimated_budget,
                project_manager_id, description, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'planning')
        `, [
            projectCode, projectName, clientName, clientContact, projectType,
            startDate, endDate, estimatedBudget, projectManagerId, description
        ]);

        res.status(201).json({
            success: true,
            message: 'Project created successfully',
            data: { id: result.id, projectCode, projectName }
        });

    } catch (error) {
        console.error('Create project error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get project details
router.get('/:id', async (req, res) => {
    try {
        const project = await db.get(`
            SELECT 
                p.*,
                u.first_name || ' ' || u.last_name as project_manager_name
            FROM projects p
            LEFT JOIN users u ON p.project_manager_id = u.id
            WHERE p.id = ?
        `, [req.params.id]);

        if (!project) {
            return res.status(404).json({
                success: false,
                message: 'Project not found'
            });
        }

        // Get cost centers
        const costCenters = await db.all(`
            SELECT * FROM project_cost_centers 
            WHERE project_id = ? AND is_active = 1
            ORDER BY cost_center_code
        `, [req.params.id]);

        // Get recent transactions
        const transactions = await db.all(`
            SELECT 
                pt.*,
                pcc.cost_center_name,
                u.first_name || ' ' || u.last_name as created_by_name
            FROM project_transactions pt
            LEFT JOIN project_cost_centers pcc ON pt.cost_center_id = pcc.id
            LEFT JOIN users u ON pt.created_by = u.id
            WHERE pt.project_id = ?
            ORDER BY pt.transaction_date DESC, pt.created_at DESC
            LIMIT 10
        `, [req.params.id]);

        res.json({
            success: true,
            data: {
                project,
                costCenters,
                recentTransactions: transactions
            }
        });

    } catch (error) {
        console.error('Get project details error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
