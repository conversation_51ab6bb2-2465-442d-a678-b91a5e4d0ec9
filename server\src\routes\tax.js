const express = require('express');
const db = require('../database/connection');

const router = express.Router();

// Get tax settings
router.get('/settings', async (req, res) => {
    try {
        const settings = await db.all(`
            SELECT * FROM tax_settings 
            WHERE is_active = 1
            ORDER BY tax_type, effective_date DESC
        `);

        res.json({
            success: true,
            data: settings
        });
    } catch (error) {
        console.error('Get tax settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get VAT report
router.get('/vat-report', async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        const vatTransactions = await db.all(`
            SELECT * FROM vat_transactions
            WHERE transaction_date BETWEEN ? AND ?
            ORDER BY transaction_date DESC
        `, [startDate, endDate]);

        res.json({
            success: true,
            data: vatTransactions
        });
    } catch (error) {
        console.error('Get VAT report error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
