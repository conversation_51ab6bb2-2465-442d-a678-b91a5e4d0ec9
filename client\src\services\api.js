import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const message = error.response?.data?.message || 'حدث خطأ في الاتصال'
    
    // Handle specific error codes
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login
      localStorage.removeItem('token')
      delete api.defaults.headers.common['Authorization']
      window.location.href = '/login'
      toast.error('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى')
    } else if (error.response?.status === 403) {
      toast.error('ليس لديك صلاحية للوصول لهذه الصفحة')
    } else if (error.response?.status >= 500) {
      toast.error('خطأ في الخادم، يرجى المحاولة لاحقاً')
    } else if (error.code === 'ECONNABORTED') {
      toast.error('انتهت مهلة الاتصال')
    } else if (!error.response) {
      toast.error('لا يمكن الاتصال بالخادم')
    }
    
    return Promise.reject(error)
  }
)

export default api
