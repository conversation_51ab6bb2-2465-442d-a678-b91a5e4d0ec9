/* Enhanced Styles for Construction ERP System */

/* Arabic Font Support */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  direction: rtl;
  text-align: right;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
}

/* Enhanced Card Styles */
.enhanced-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.enhanced-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--border-color);
}

.enhanced-card.clickable {
  cursor: pointer;
}

.enhanced-card.clickable:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

/* Enhanced Button Styles */
.btn-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all var(--transition-fast);
  border: none;
  cursor: pointer;
  text-decoration: none;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #475569;
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #059669;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-warning:hover {
  background-color: #d97706;
  transform: translateY(-1px);
}

.btn-error {
  background-color: var(--error-color);
  color: white;
}

.btn-error:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
}

/* Enhanced Input Styles */
.input-enhanced {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.input-enhanced:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.input-enhanced:hover {
  border-color: var(--secondary-color);
}

/* Enhanced Table Styles */
.table-enhanced {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table-enhanced th {
  background-color: var(--bg-tertiary);
  padding: 1rem;
  text-align: right;
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--border-color);
}

.table-enhanced td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  color: var(--text-primary);
}

.table-enhanced tr:hover {
  background-color: var(--bg-tertiary);
}

.table-enhanced tr:last-child td {
  border-bottom: none;
}

/* Enhanced Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  gap: 0.25rem;
}

.status-success {
  background-color: rgb(220 252 231);
  color: rgb(22 101 52);
}

.status-warning {
  background-color: rgb(254 243 199);
  color: rgb(146 64 14);
}

.status-error {
  background-color: rgb(254 226 226);
  color: rgb(153 27 27);
}

.status-info {
  background-color: rgb(219 234 254);
  color: rgb(30 58 138);
}

.status-neutral {
  background-color: rgb(241 245 249);
  color: rgb(51 65 85);
}

/* Enhanced Navigation */
.nav-enhanced {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
  margin: 0.25rem;
  gap: 0.75rem;
}

.nav-item:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: rgb(239 246 255);
  color: var(--primary-color);
  border-right: 4px solid var(--primary-color);
}

/* Enhanced Progress Bar */
.progress-enhanced {
  width: 100%;
  height: 0.5rem;
  background-color: var(--bg-tertiary);
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 9999px;
  transition: width var(--transition-normal);
}

.progress-success {
  background-color: var(--success-color);
}

.progress-warning {
  background-color: var(--warning-color);
}

.progress-error {
  background-color: var(--error-color);
}

/* Enhanced Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  animation: modalSlideIn var(--transition-normal);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced Loading Spinner */
.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Tooltip */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: var(--text-primary);
  color: var(--bg-primary);
  text-align: center;
  border-radius: var(--radius-md);
  padding: 0.5rem;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity var(--transition-fast);
  font-size: 0.75rem;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Enhanced Animations */
.fade-in {
  animation: fadeIn var(--transition-normal);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-in-right {
  animation: slideInRight var(--transition-normal);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp var(--transition-normal);
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-card {
    margin: 0.5rem;
    border-radius: var(--radius-md);
  }
  
  .btn-enhanced {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
  
  .table-enhanced {
    font-size: 0.8rem;
  }
  
  .table-enhanced th,
  .table-enhanced td {
    padding: 0.5rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    --border-color: #334155;
    --border-light: #475569;
  }
}

/* Print Styles */
@media print {
  .enhanced-card {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }
  
  .btn-enhanced {
    display: none;
  }
  
  .nav-enhanced {
    display: none;
  }
}
