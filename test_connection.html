<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال - نظام إدارة المقاولات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ نظام إدارة المقاولات</h1>
        
        <div id="status" class="status info">
            <div class="loading"></div>
            جاري فحص الاتصال...
        </div>
        
        <div id="details" style="display: none;">
            <h3>تفاصيل الاتصال:</h3>
            <p><strong>الخادم:</strong> <span id="server-status">❌</span></p>
            <p><strong>العميل:</strong> <span id="client-status">❌</span></p>
        </div>
        
        <div id="actions" style="display: none;">
            <a href="http://localhost:5173" class="btn btn-success">
                🚀 دخول النظام
            </a>
            <button onclick="testConnection()" class="btn">
                🔄 إعادة الفحص
            </button>
        </div>
        
        <div id="login-info" style="display: none; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3>🔑 بيانات تسجيل الدخول:</h3>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>
    </div>

    <script>
        async function testConnection() {
            const statusDiv = document.getElementById('status');
            const detailsDiv = document.getElementById('details');
            const actionsDiv = document.getElementById('actions');
            const loginInfoDiv = document.getElementById('login-info');
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '<div class="loading"></div> جاري فحص الاتصال...';
            detailsDiv.style.display = 'none';
            actionsDiv.style.display = 'none';
            loginInfoDiv.style.display = 'none';
            
            let serverOk = false;
            let clientOk = false;
            
            // Test server
            try {
                const response = await fetch('http://localhost:3001/health');
                if (response.ok) {
                    serverOk = true;
                    document.getElementById('server-status').textContent = '✅ يعمل';
                } else {
                    document.getElementById('server-status').textContent = '❌ خطأ';
                }
            } catch (error) {
                document.getElementById('server-status').textContent = '❌ غير متصل';
            }
            
            // Test client
            try {
                const response = await fetch('http://localhost:5173');
                if (response.ok) {
                    clientOk = true;
                    document.getElementById('client-status').textContent = '✅ يعمل';
                } else {
                    document.getElementById('client-status').textContent = '❌ خطأ';
                }
            } catch (error) {
                document.getElementById('client-status').textContent = '❌ غير متصل';
            }
            
            detailsDiv.style.display = 'block';
            
            if (serverOk && clientOk) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ النظام يعمل بشكل صحيح!';
                actionsDiv.style.display = 'block';
                loginInfoDiv.style.display = 'block';
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ هناك مشكلة في الاتصال. تأكد من تشغيل الخوادم.';
                actionsDiv.innerHTML = `
                    <button onclick="testConnection()" class="btn">🔄 إعادة الفحص</button>
                    <p style="margin-top: 20px; color: #666;">
                        لتشغيل النظام، شغل الملف: <strong>quick_start.bat</strong>
                    </p>
                `;
                actionsDiv.style.display = 'block';
            }
        }
        
        // Test connection on page load
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
