const express = require('express');
const { authMiddleware } = require('../middleware/auth');
const SecurityService = require('../services/securityService');

const router = express.Router();

// إنشاء مفتاح المصادقة الثنائية
router.post('/2fa/generate', authMiddleware, async (req, res) => {
    try {
        const result = await SecurityService.generateTwoFactorSecret(req.user.id);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('Generate 2FA secret error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تفعيل المصادقة الثنائية
router.post('/2fa/enable', authMiddleware, async (req, res) => {
    try {
        const { token } = req.body;

        if (!token) {
            return res.status(400).json({
                success: false,
                message: 'Verification token is required'
            });
        }

        await SecurityService.enableTwoFactor(req.user.id, token);

        res.json({
            success: true,
            message: 'Two-factor authentication enabled successfully'
        });

    } catch (error) {
        console.error('Enable 2FA error:', error);
        res.status(400).json({
            success: false,
            message: error.message || 'Failed to enable two-factor authentication'
        });
    }
});

// إلغاء تفعيل المصادقة الثنائية
router.post('/2fa/disable', authMiddleware, async (req, res) => {
    try {
        const { password } = req.body;

        if (!password) {
            return res.status(400).json({
                success: false,
                message: 'Password is required'
            });
        }

        await SecurityService.disableTwoFactor(req.user.id, password);

        res.json({
            success: true,
            message: 'Two-factor authentication disabled successfully'
        });

    } catch (error) {
        console.error('Disable 2FA error:', error);
        res.status(400).json({
            success: false,
            message: error.message || 'Failed to disable two-factor authentication'
        });
    }
});

// التحقق من قوة كلمة المرور
router.post('/password/check-strength', async (req, res) => {
    try {
        const { password } = req.body;

        if (!password) {
            return res.status(400).json({
                success: false,
                message: 'Password is required'
            });
        }

        const result = SecurityService.checkPasswordStrength(password);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('Check password strength error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// طلب استرداد كلمة المرور
router.post('/password/reset-request', async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email is required'
            });
        }

        const token = await SecurityService.generatePasswordResetToken(email);

        // في بيئة الإنتاج، يجب إرسال الرمز عبر البريد الإلكتروني
        // هنا نعيده في الاستجابة للاختبار فقط
        res.json({
            success: true,
            message: 'Password reset token generated',
            token: process.env.NODE_ENV === 'development' ? token : undefined
        });

    } catch (error) {
        console.error('Password reset request error:', error);
        res.status(400).json({
            success: false,
            message: error.message || 'Failed to generate reset token'
        });
    }
});

// إعادة تعيين كلمة المرور
router.post('/password/reset', async (req, res) => {
    try {
        const { token, newPassword } = req.body;

        if (!token || !newPassword) {
            return res.status(400).json({
                success: false,
                message: 'Token and new password are required'
            });
        }

        await SecurityService.resetPassword(token, newPassword);

        res.json({
            success: true,
            message: 'Password reset successfully'
        });

    } catch (error) {
        console.error('Password reset error:', error);
        res.status(400).json({
            success: false,
            message: error.message || 'Failed to reset password'
        });
    }
});

// الحصول على سجلات الأمان (للمدراء فقط)
router.get('/logs', authMiddleware, async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        const { page = 1, limit = 50, userId, activityType, suspicious } = req.query;
        const offset = (page - 1) * limit;

        const db = require('../database/connection');
        
        let whereClause = '1=1';
        let params = [];

        if (userId) {
            whereClause += ' AND user_id = ?';
            params.push(userId);
        }

        if (activityType) {
            whereClause += ' AND activity_type = ?';
            params.push(activityType);
        }

        if (suspicious !== undefined) {
            whereClause += ' AND is_suspicious = ?';
            params.push(suspicious === 'true' ? 1 : 0);
        }

        const logs = await db.all(`
            SELECT 
                sl.*,
                u.first_name || ' ' || u.last_name as user_name,
                u.email as user_email
            FROM security_logs sl
            LEFT JOIN users u ON sl.user_id = u.id
            WHERE ${whereClause}
            ORDER BY sl.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, limit, offset]);

        const totalCount = await db.get(`
            SELECT COUNT(*) as count 
            FROM security_logs 
            WHERE ${whereClause}
        `, params);

        res.json({
            success: true,
            data: {
                logs,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: totalCount.count,
                    pages: Math.ceil(totalCount.count / limit)
                }
            }
        });

    } catch (error) {
        console.error('Get security logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// الحصول على إحصائيات الأمان (للمدراء فقط)
router.get('/stats', authMiddleware, async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        const db = require('../database/connection');

        const stats = await Promise.all([
            // إجمالي محاولات الدخول اليوم
            db.get(`
                SELECT COUNT(*) as count 
                FROM security_logs 
                WHERE activity_type LIKE '%login%' 
                AND DATE(created_at) = DATE('now')
            `),
            // محاولات الدخول الفاشلة اليوم
            db.get(`
                SELECT COUNT(*) as count 
                FROM security_logs 
                WHERE activity_type = 'failed_login' 
                AND DATE(created_at) = DATE('now')
            `),
            // الأنشطة المشبوهة هذا الأسبوع
            db.get(`
                SELECT COUNT(*) as count 
                FROM security_logs 
                WHERE is_suspicious = 1 
                AND created_at >= DATE('now', '-7 days')
            `),
            // المستخدمين مع المصادقة الثنائية
            db.get(`
                SELECT COUNT(*) as count 
                FROM users 
                WHERE two_factor_enabled = 1
            `),
            // الجلسات النشطة
            db.get(`
                SELECT COUNT(*) as count 
                FROM user_sessions 
                WHERE expires_at > datetime('now')
            `)
        ]);

        const securityStats = {
            todayLogins: stats[0].count,
            todayFailedLogins: stats[1].count,
            weekSuspiciousActivities: stats[2].count,
            usersWithTwoFactor: stats[3].count,
            activeSessions: stats[4].count,
            loginSuccessRate: stats[0].count > 0 
                ? ((stats[0].count - stats[1].count) / stats[0].count * 100).toFixed(1)
                : 100
        };

        res.json({
            success: true,
            data: securityStats
        });

    } catch (error) {
        console.error('Get security stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// الحصول على إعدادات الأمان (للمدراء فقط)
router.get('/settings', authMiddleware, async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        const db = require('../database/connection');
        
        const settings = await db.all(`
            SELECT setting_name, setting_value, description 
            FROM security_settings 
            ORDER BY setting_name
        `);

        res.json({
            success: true,
            data: settings
        });

    } catch (error) {
        console.error('Get security settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحديث إعدادات الأمان (للمدراء فقط)
router.put('/settings', authMiddleware, async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        const { settings } = req.body;

        if (!settings || !Array.isArray(settings)) {
            return res.status(400).json({
                success: false,
                message: 'Settings array is required'
            });
        }

        const db = require('../database/connection');

        for (const setting of settings) {
            await db.run(`
                UPDATE security_settings 
                SET setting_value = ?, updated_at = datetime('now')
                WHERE setting_name = ?
            `, [setting.value, setting.name]);
        }

        // تسجيل النشاط
        await SecurityService.logSecurityEvent(
            req.user.id, 
            'settings_update', 
            'Security settings updated'
        );

        res.json({
            success: true,
            message: 'Security settings updated successfully'
        });

    } catch (error) {
        console.error('Update security settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
