const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const db = require('../database/connection');
const { generateToken, authMiddleware } = require('../middleware/auth');
const { logSystemEvent } = require('../middleware/audit');

const router = express.Router();

// Login
router.post('/login', [
    body('username').notEmpty().withMessage('Username is required'),
    body('password').notEmpty().withMessage('Password is required')
], async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { username, password } = req.body;
        const clientIP = req.ip || req.connection.remoteAddress;

        // Find user
        const user = await db.get(
            'SELECT * FROM users WHERE username = ? OR email = ?',
            [username, username]
        );

        if (!user) {
            await logSystemEvent('WARNING', 'AUTH', 'Failed login attempt - user not found', 
                { username }, null, clientIP);
            
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Check if user is active
        if (!user.is_active) {
            await logSystemEvent('WARNING', 'AUTH', 'Failed login attempt - inactive user', 
                { username, userId: user.id }, user.id, clientIP);
            
            return res.status(401).json({
                success: false,
                message: 'Account is deactivated'
            });
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        
        if (!isValidPassword) {
            await logSystemEvent('WARNING', 'AUTH', 'Failed login attempt - invalid password', 
                { username, userId: user.id }, user.id, clientIP);
            
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Update last login
        await db.run(
            'UPDATE users SET last_login = datetime("now") WHERE id = ?',
            [user.id]
        );

        // Generate token
        const token = generateToken(user.id);

        // Log successful login
        await logSystemEvent('INFO', 'AUTH', 'Successful login', 
            { username, userId: user.id }, user.id, clientIP);

        res.json({
            success: true,
            message: 'Login successful',
            data: {
                token,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    firstName: user.first_name,
                    lastName: user.last_name,
                    role: user.role
                }
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Register (Admin only)
router.post('/register', authMiddleware, [
    body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('role').isIn(['admin', 'accountant', 'project_manager', 'hr_manager', 'user'])
        .withMessage('Invalid role')
], async (req, res) => {
    try {
        // Only admin can create users
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Only administrators can create users'
            });
        }

        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { username, email, password, firstName, lastName, role } = req.body;

        // Check if user already exists
        const existingUser = await db.get(
            'SELECT id FROM users WHERE username = ? OR email = ?',
            [username, email]
        );

        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: 'Username or email already exists'
            });
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Create user
        const result = await db.run(`
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, is_active)
            VALUES (?, ?, ?, ?, ?, ?, 1)
        `, [username, email, hashedPassword, firstName, lastName, role]);

        // Log user creation
        await logSystemEvent('INFO', 'AUTH', 'User created', 
            { newUserId: result.id, username, email, role }, req.user.id, req.ip);

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            data: {
                id: result.id,
                username,
                email,
                firstName,
                lastName,
                role
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get current user profile
router.get('/profile', authMiddleware, async (req, res) => {
    try {
        const user = await db.get(`
            SELECT id, username, email, first_name, last_name, role, 
                   last_login, created_at
            FROM users 
            WHERE id = ?
        `, [req.user.id]);

        res.json({
            success: true,
            data: {
                id: user.id,
                username: user.username,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                role: user.role,
                lastLogin: user.last_login,
                createdAt: user.created_at
            }
        });

    } catch (error) {
        console.error('Profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Change password
router.put('/change-password', authMiddleware, [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword').isLength({ min: 6 }).withMessage('New password must be at least 6 characters')
], async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { currentPassword, newPassword } = req.body;

        // Get current user with password
        const user = await db.get(
            'SELECT password_hash FROM users WHERE id = ?',
            [req.user.id]
        );

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
        
        if (!isValidPassword) {
            return res.status(400).json({
                success: false,
                message: 'Current password is incorrect'
            });
        }

        // Hash new password
        const hashedNewPassword = await bcrypt.hash(newPassword, 10);

        // Update password
        await db.run(
            'UPDATE users SET password_hash = ?, updated_at = datetime("now") WHERE id = ?',
            [hashedNewPassword, req.user.id]
        );

        // Log password change
        await logSystemEvent('INFO', 'AUTH', 'Password changed', 
            { userId: req.user.id }, req.user.id, req.ip);

        res.json({
            success: true,
            message: 'Password changed successfully'
        });

    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Logout (client-side token removal, but log the event)
router.post('/logout', authMiddleware, async (req, res) => {
    try {
        // Log logout event
        await logSystemEvent('INFO', 'AUTH', 'User logout', 
            { userId: req.user.id }, req.user.id, req.ip);

        res.json({
            success: true,
            message: 'Logged out successfully'
        });

    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
