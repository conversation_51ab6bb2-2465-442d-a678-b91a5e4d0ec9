import { useState } from 'react'

function GeneralLedger() {
  const [selectedAccount, setSelectedAccount] = useState('1110')
  const [dateFrom, setDateFrom] = useState('2024-01-01')
  const [dateTo, setDateTo] = useState('2024-01-31')

  // بيانات دفتر الأستاذ
  const ledgerData = {
    '1110': {
      name: 'النقدية بالصندوق',
      openingBalance: 30000,
      transactions: [
        { date: '2024-01-02', ref: 'JE-001', description: 'رصيد افتتاحي', debit: 30000, credit: 0 },
        { date: '2024-01-05', ref: 'JE-002', description: 'تحصيل من العميل أحمد علي', debit: 25000, credit: 0 },
        { date: '2024-01-08', ref: 'JE-003', description: 'دفع مصروفات نثرية', debit: 0, credit: 3000 },
        { date: '2024-01-12', ref: 'JE-004', description: 'إيداع في البنك', debit: 0, credit: 20000 },
        { date: '2024-01-15', ref: 'JE-005', description: 'تحصيل نقدي من مشروع الفيلا', debit: 15000, credit: 0 },
        { date: '2024-01-20', ref: 'JE-006', description: 'دفع أجور عمال يومية', debit: 0, credit: 8000 },
        { date: '2024-01-25', ref: 'JE-007', description: 'سحب من البنك', debit: 10000, credit: 0 },
        { date: '2024-01-28', ref: 'JE-008', description: 'دفع فاتورة كهرباء', debit: 0, credit: 1000 }
      ]
    },
    '1120': {
      name: 'النقدية بالبنوك',
      openingBalance: 200000,
      transactions: [
        { date: '2024-01-02', ref: 'JE-001', description: 'رصيد افتتاحي', debit: 200000, credit: 0 },
        { date: '2024-01-12', ref: 'JE-004', description: 'إيداع من الصندوق', debit: 20000, credit: 0 },
        { date: '2024-01-14', ref: 'JE-009', description: 'تحويل من العميل محمد أحمد', debit: 50000, credit: 0 },
        { date: '2024-01-18', ref: 'JE-010', description: 'دفع للمورد شركة الإنشاءات', debit: 0, credit: 35000 },
        { date: '2024-01-22', ref: 'JE-011', description: 'دفع رواتب شهر يناير', debit: 0, credit: 45000 },
        { date: '2024-01-25', ref: 'JE-007', description: 'سحب للصندوق', debit: 0, credit: 10000 },
        { date: '2024-01-30', ref: 'JE-012', description: 'فوائد بنكية', debit: 2000, credit: 0 }
      ]
    },
    '1130': {
      name: 'حسابات العملاء',
      openingBalance: 150000,
      transactions: [
        { date: '2024-01-02', ref: 'JE-001', description: 'رصيد افتتاحي', debit: 150000, credit: 0 },
        { date: '2024-01-05', ref: 'JE-002', description: 'تحصيل من العميل أحمد علي', debit: 0, credit: 25000 },
        { date: '2024-01-10', ref: 'JE-013', description: 'فاتورة مبيعات للعميل سارة محمد', debit: 40000, credit: 0 },
        { date: '2024-01-14', ref: 'JE-009', description: 'تحصيل من العميل محمد أحمد', debit: 0, credit: 50000 },
        { date: '2024-01-18', ref: 'JE-014', description: 'فاتورة مشروع المجمع التجاري', debit: 75000, credit: 0 },
        { date: '2024-01-26', ref: 'JE-015', description: 'تحصيل جزئي من سارة محمد', debit: 0, credit: 15000 },
        { date: '2024-01-29', ref: 'JE-016', description: 'فاتورة أعمال إضافية', debit: 25000, credit: 0 }
      ]
    }
  }

  const accounts = [
    { code: '1110', name: 'النقدية بالصندوق' },
    { code: '1120', name: 'النقدية بالبنوك' },
    { code: '1130', name: 'حسابات العملاء' },
    { code: '2110', name: 'حسابات الموردين' },
    { code: '4100', name: 'إيرادات المشاريع' },
    { code: '5110', name: 'مواد البناء' }
  ]

  const calculateRunningBalance = (transactions, openingBalance) => {
    let balance = openingBalance
    return transactions.map(transaction => {
      balance += transaction.debit - transaction.credit
      return { ...transaction, balance }
    })
  }

  const currentLedger = ledgerData[selectedAccount]
  const transactionsWithBalance = currentLedger ? 
    calculateRunningBalance(currentLedger.transactions, currentLedger.openingBalance) : []

  const totalDebits = transactionsWithBalance.reduce((sum, t) => sum + t.debit, 0)
  const totalCredits = transactionsWithBalance.reduce((sum, t) => sum + t.credit, 0)
  const finalBalance = currentLedger ? 
    currentLedger.openingBalance + totalDebits - totalCredits : 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                📚 دفتر الأستاذ العام
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => alert('📊 جاري تصدير كشف الحساب...')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📋 تصدير PDF
              </button>
              <button
                onClick={() => alert('🔍 جاري البحث المتقدم...')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                🔍 بحث متقدم
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          
          {/* قائمة الحسابات */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">📋 اختيار الحساب</h3>
            
            {/* فلاتر التاريخ */}
            <div className="mb-4 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>

            {/* قائمة الحسابات */}
            <div className="space-y-2">
              {accounts.map(account => (
                <button
                  key={account.code}
                  onClick={() => setSelectedAccount(account.code)}
                  className={`w-full text-right px-3 py-2 rounded-lg text-sm transition-colors ${
                    selectedAccount === account.code
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <div className="font-medium">{account.code}</div>
                  <div className="text-xs">{account.name}</div>
                </button>
              ))}
            </div>
          </div>

          {/* كشف الحساب */}
          <div className="lg:col-span-3 bg-white rounded-lg shadow-sm">
            <div className="p-6">
              {currentLedger ? (
                <>
                  {/* رأس الكشف */}
                  <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h2 className="text-lg font-medium text-gray-900 mb-2">
                      كشف حساب: {selectedAccount} - {currentLedger.name}
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">الفترة:</span>
                        <span className="mr-2 font-medium">{dateFrom} إلى {dateTo}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">الرصيد الافتتاحي:</span>
                        <span className="mr-2 font-medium text-blue-600">
                          {currentLedger.openingBalance.toLocaleString('ar-EG')} ج.م
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-500">الرصيد الختامي:</span>
                        <span className="mr-2 font-medium text-green-600">
                          {finalBalance.toLocaleString('ar-EG')} ج.م
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* جدول الحركات */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            التاريخ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المرجع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            البيان
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            مدين
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            دائن
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الرصيد
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {transactionsWithBalance.map((transaction, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {transaction.date}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                              {transaction.ref}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {transaction.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                              {transaction.debit > 0 ? transaction.debit.toLocaleString('ar-EG') : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                              {transaction.credit > 0 ? transaction.credit.toLocaleString('ar-EG') : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {transaction.balance.toLocaleString('ar-EG')}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot className="bg-gray-100">
                        <tr>
                          <td colSpan="3" className="px-6 py-4 text-sm font-medium text-gray-900">
                            الإجمالي
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                            {totalDebits.toLocaleString('ar-EG')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-red-600">
                            {totalCredits.toLocaleString('ar-EG')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                            {finalBalance.toLocaleString('ar-EG')}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>

                  {/* إحصائيات سريعة */}
                  <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-green-800 text-sm font-medium">إجمالي المدين</div>
                      <div className="text-green-900 text-lg font-bold">
                        {totalDebits.toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="text-red-800 text-sm font-medium">إجمالي الدائن</div>
                      <div className="text-red-900 text-lg font-bold">
                        {totalCredits.toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-blue-800 text-sm font-medium">صافي الحركة</div>
                      <div className="text-blue-900 text-lg font-bold">
                        {(totalDebits - totalCredits).toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-gray-800 text-sm font-medium">عدد الحركات</div>
                      <div className="text-gray-900 text-lg font-bold">
                        {transactionsWithBalance.length} حركة
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center text-gray-500 py-12">
                  <div className="text-4xl mb-4">📚</div>
                  <h3 className="text-lg font-medium mb-2">اختر حساباً لعرض كشف الحساب</h3>
                  <p className="text-sm">سيتم عرض جميع حركات الحساب في الفترة المحددة</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GeneralLedger
