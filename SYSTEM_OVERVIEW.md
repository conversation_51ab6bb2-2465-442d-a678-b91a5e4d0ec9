# نظام إدارة المقاولات - نظرة عامة

## 🏗️ نظام محاسبي شامل للمقاولات مع الامتثال الضريبي المصري والذكاء الاصطناعي

### ✅ الميزات المكتملة

#### 🔐 نظام المصادقة والأمان
- تسجيل دخول آمن مع JWT
- إدارة المستخدمين والأدوار
- تسجيل العمليات (Audit Trail)
- حماية متقدمة للبيانات

#### 📊 المحاسبة العامة
- **دليل الحسابات**: نظام شامل متوافق مع المعايير المصرية
- **دفتر الأستاذ العام**: تسجيل تلقائي للقيود من جميع الوحدات
- **ميزان المراجعة**: تقارير مالية دقيقة ومتوازنة
- **القيود المحاسبية**: نظام القيد المزدوج الكامل

#### 🏗️ إدارة المشاريع
- تسجيل المشاريع بأنواعها (مدني، إنشائي، تشطيبات، كهرباء، سباكة، بنية تحتية)
- مراكز التكلفة وتتبع الميزانيات
- ربط تكاليف المشاريع بالحسابات العامة
- تقارير أداء المشاريع

#### 📦 إدارة المخزون
- تسجيل الأصناف والمواد
- حركات الاستلام والصرف
- تخصيص المواد للمشاريع
- تنبيهات الحد الأدنى للمخزون

#### 🧾 الفواتير والمدفوعات
- **فواتير المبيعات والمشتريات**
- **نظام المدفوعات والمقبوضات**
- **ربط تلقائي بالحسابات العامة**
- **تتبع حالة الفواتير والمدفوعات**

#### 🏦 الخزينة والبنوك
- إدارة الحسابات البنكية
- تسجيل الحركات البنكية
- مطابقة الحسابات البنكية
- تقارير التدفق النقدي

#### 👥 الموارد البشرية
- سجلات الموظفين
- نظام الحضور والانصراف
- حساب الرواتب والمستحقات
- إدارة السلف والعهد

#### 🧾 الامتثال الضريبي المصري
- **ضريبة القيمة المضافة (14%)**
- **ضريبة الخصم والإضافة (10%)**
- **ضريبة الدخل على الشركات**
- **نموذج 41 الضريبي**
- **تقارير ضريبية شاملة**

#### 🤖 التحليلات الذكية
- توقعات التدفق النقدي
- تحليل تجاوز التكاليف
- توقع تأخير المدفوعات
- توصيات تحسين الأداء
- كشف المخاطر المبكر

#### 📈 التقارير
- تقارير مالية شاملة
- تقارير المشاريع والتكاليف
- تقارير الضرائب
- تقارير الموارد البشرية
- إمكانية التصدير (PDF/Excel)

### 🛠️ التقنيات المستخدمة

#### Frontend (العميل)
- **React 18** مع Vite
- **Tailwind CSS** للتصميم
- **React Router** للتنقل
- **React Query** لإدارة البيانات
- **Zustand** لإدارة الحالة
- **React Hook Form** للنماذج
- **Heroicons** للأيقونات

#### Backend (الخادم)
- **Node.js** مع Express
- **SQLite** قاعدة البيانات
- **JWT** للمصادقة
- **bcryptjs** لتشفير كلمات المرور
- **Express Validator** للتحقق من البيانات
- **Morgan** لتسجيل الطلبات

#### الأمان والحماية
- تشفير كلمات المرور
- حماية من CSRF
- تسجيل جميع العمليات
- صلاحيات متدرجة للمستخدمين

### 🚀 كيفية التشغيل

#### الطريقة السريعة
```bash
# تشغيل الملف التلقائي
start.bat
```

#### الطريقة اليدوية
```bash
# تثبيت التبعيات
npm run install:all

# إنشاء قاعدة البيانات
cd server
node src/database/simple_migrate.js
node src/database/simple_seed.js

# تشغيل النظام
cd ..
npm run dev
```

### 🔑 بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### 🌐 عناوين الوصول
- **الواجهة الأمامية**: http://localhost:5173
- **API الخادم**: http://localhost:3001

### 📋 الوحدات الرئيسية

1. **لوحة التحكم** - نظرة عامة على النظام
2. **المحاسبة العامة** - دليل الحسابات والقيود
3. **إدارة المشاريع** - المشاريع ومراكز التكلفة
4. **المخزون** - إدارة المواد والأصناف
5. **الفواتير** - فواتير المبيعات والمشتريات
6. **المدفوعات** - المقبوضات والمدفوعات
7. **الخزينة والبنوك** - الحسابات البنكية
8. **الموارد البشرية** - الموظفين والرواتب
9. **الضرائب** - الامتثال الضريبي المصري
10. **التقارير** - تقارير شاملة
11. **التحليلات الذكية** - AI والتوقعات
12. **الإعدادات** - إعدادات النظام

### 🎯 المميزات الخاصة

#### ✅ الامتثال المصري الكامل
- معايير المحاسبة المصرية
- النظام الضريبي المصري
- نموذج 41 الضريبي
- اللغة العربية كاملة

#### ✅ التكامل التلقائي
- ربط تلقائي بين جميع الوحدات
- ترحيل تلقائي للحسابات العامة
- تحديث تلقائي للأرصدة
- تسجيل تلقائي للعمليات

#### ✅ الذكاء الاصطناعي
- توقعات مالية دقيقة
- تحليل المخاطر
- توصيات التحسين
- كشف الأنماط

### 📞 الدعم والتطوير
هذا النظام قابل للتوسع والتطوير حسب احتياجات الشركة. يمكن إضافة المزيد من الوحدات والميزات حسب الطلب.

---
**تم تطوير النظام باستخدام أحدث التقنيات لضمان الأداء والأمان والموثوقية**
