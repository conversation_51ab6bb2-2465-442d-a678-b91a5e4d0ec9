// Export utilities for PDF, Excel, and other formats

// Simulate PDF export
export const exportToPDF = (data, filename = 'report') => {
  return new Promise((resolve) => {
    // Simulate PDF generation
    setTimeout(() => {
      // Create a simple PDF-like content
      const pdfContent = `
        تقرير النظام المحاسبي
        =====================
        
        تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}
        
        البيانات:
        ${JSON.stringify(data, null, 2)}
        
        تم إنشاء هذا التقرير بواسطة نظام إدارة المقاولات
      `
      
      // Create blob and download
      const blob = new Blob([pdfContent], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${filename}-${new Date().toISOString().split('T')[0]}.txt`
      a.click()
      URL.revokeObjectURL(url)
      
      resolve({ success: true, message: 'تم تصدير PDF بنجاح' })
    }, 1500)
  })
}

// Simulate Excel export
export const exportToExcel = (data, filename = 'data') => {
  return new Promise((resolve) => {
    // Simulate Excel generation
    setTimeout(() => {
      // Convert data to CSV format (simplified Excel)
      let csvContent = 'البيانات المصدرة\n'
      csvContent += 'التاريخ,النوع,القيمة,الوصف\n'
      
      if (Array.isArray(data)) {
        data.forEach(item => {
          csvContent += `${item.date || new Date().toLocaleDateString('ar-EG')},${item.type || 'عام'},${item.value || 0},${item.description || 'بدون وصف'}\n`
        })
      } else {
        csvContent += `${new Date().toLocaleDateString('ar-EG')},تقرير,${data.total || 0},بيانات النظام\n`
      }
      
      // Create blob and download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${filename}-${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      URL.revokeObjectURL(url)
      
      resolve({ success: true, message: 'تم تصدير Excel بنجاح' })
    }, 1500)
  })
}

// Import from Excel/CSV
export const importFromExcel = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const content = e.target.result
        const lines = content.split('\n')
        const data = []
        
        // Skip header and process data
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim()
          if (line) {
            const columns = line.split(',')
            data.push({
              date: columns[0] || '',
              type: columns[1] || '',
              value: parseFloat(columns[2]) || 0,
              description: columns[3] || ''
            })
          }
        }
        
        resolve({ 
          success: true, 
          data: data,
          message: `تم استيراد ${data.length} سجل بنجاح`
        })
      } catch (error) {
        reject({ 
          success: false, 
          message: 'خطأ في قراءة الملف'
        })
      }
    }
    
    reader.onerror = () => {
      reject({ 
        success: false, 
        message: 'فشل في قراءة الملف'
      })
    }
    
    reader.readAsText(file)
  })
}

// Export to JSON
export const exportToJSON = (data, filename = 'data') => {
  const jsonData = {
    exportDate: new Date().toISOString(),
    system: 'نظام إدارة المقاولات',
    version: '1.0.0',
    data: data
  }
  
  const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${filename}-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  return { success: true, message: 'تم تصدير JSON بنجاح' }
}

// Generate sample data for testing
export const generateSampleData = () => {
  return [
    {
      date: '2024-01-15',
      type: 'إيراد',
      value: 50000,
      description: 'دفعة من مشروع الفيلا'
    },
    {
      date: '2024-01-20',
      type: 'مصروف',
      value: 25000,
      description: 'شراء مواد بناء'
    },
    {
      date: '2024-01-25',
      type: 'إيراد',
      value: 75000,
      description: 'دفعة من مشروع المجمع التجاري'
    },
    {
      date: '2024-02-01',
      type: 'مصروف',
      value: 15000,
      description: 'رواتب العمال'
    },
    {
      date: '2024-02-05',
      type: 'إيراد',
      value: 100000,
      description: 'دفعة نهائية مشروع الفيلا'
    }
  ]
}

// Format currency for display
export const formatCurrency = (amount, currency = 'EGP') => {
  const formatter = new Intl.NumberFormat('ar-EG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  })
  return formatter.format(amount)
}

// Format date for display
export const formatDate = (date, locale = 'ar-EG') => {
  return new Date(date).toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
