// خدمة الأمان المتقدم
const crypto = require('crypto');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require('../database/connection');

class SecurityService {
    
    // إنشاء مفتاح المصادقة الثنائية
    static async generateTwoFactorSecret(userId) {
        try {
            const secret = speakeasy.generateSecret({
                name: `Construction ERP (${userId})`,
                issuer: 'Construction ERP System',
                length: 32
            });

            // حفظ المفتاح في قاعدة البيانات
            await db.run(`
                UPDATE users 
                SET two_factor_secret = ?, two_factor_enabled = 0 
                WHERE id = ?
            `, [secret.base32, userId]);

            // إنشاء QR Code
            const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

            return {
                secret: secret.base32,
                qrCode: qrCodeUrl,
                manualEntryKey: secret.base32
            };

        } catch (error) {
            console.error('Error generating 2FA secret:', error);
            throw error;
        }
    }

    // تفعيل المصادقة الثنائية
    static async enableTwoFactor(userId, token) {
        try {
            // الحصول على المفتاح السري
            const user = await db.get(
                'SELECT two_factor_secret FROM users WHERE id = ?',
                [userId]
            );

            if (!user || !user.two_factor_secret) {
                throw new Error('Two-factor secret not found');
            }

            // التحقق من الرمز
            const verified = speakeasy.totp.verify({
                secret: user.two_factor_secret,
                encoding: 'base32',
                token: token,
                window: 2
            });

            if (!verified) {
                throw new Error('Invalid verification code');
            }

            // تفعيل المصادقة الثنائية
            await db.run(`
                UPDATE users 
                SET two_factor_enabled = 1 
                WHERE id = ?
            `, [userId]);

            return { success: true };

        } catch (error) {
            console.error('Error enabling 2FA:', error);
            throw error;
        }
    }

    // إلغاء تفعيل المصادقة الثنائية
    static async disableTwoFactor(userId, password) {
        try {
            // التحقق من كلمة المرور
            const user = await db.get(
                'SELECT password FROM users WHERE id = ?',
                [userId]
            );

            if (!user) {
                throw new Error('User not found');
            }

            const isValidPassword = await bcrypt.compare(password, user.password);
            if (!isValidPassword) {
                throw new Error('Invalid password');
            }

            // إلغاء تفعيل المصادقة الثنائية
            await db.run(`
                UPDATE users 
                SET two_factor_enabled = 0, two_factor_secret = NULL 
                WHERE id = ?
            `, [userId]);

            return { success: true };

        } catch (error) {
            console.error('Error disabling 2FA:', error);
            throw error;
        }
    }

    // التحقق من رمز المصادقة الثنائية
    static async verifyTwoFactorToken(userId, token) {
        try {
            const user = await db.get(
                'SELECT two_factor_secret, two_factor_enabled FROM users WHERE id = ?',
                [userId]
            );

            if (!user || !user.two_factor_enabled || !user.two_factor_secret) {
                return false;
            }

            return speakeasy.totp.verify({
                secret: user.two_factor_secret,
                encoding: 'base32',
                token: token,
                window: 2
            });

        } catch (error) {
            console.error('Error verifying 2FA token:', error);
            return false;
        }
    }

    // تشفير البيانات الحساسة
    static encryptSensitiveData(data) {
        try {
            const algorithm = 'aes-256-gcm';
            const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'default-key', 'salt', 32);
            const iv = crypto.randomBytes(16);
            
            const cipher = crypto.createCipher(algorithm, key);
            cipher.setAAD(Buffer.from('additional-data'));
            
            let encrypted = cipher.update(data, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const authTag = cipher.getAuthTag();
            
            return {
                encrypted,
                iv: iv.toString('hex'),
                authTag: authTag.toString('hex')
            };

        } catch (error) {
            console.error('Error encrypting data:', error);
            throw error;
        }
    }

    // فك تشفير البيانات الحساسة
    static decryptSensitiveData(encryptedData, iv, authTag) {
        try {
            const algorithm = 'aes-256-gcm';
            const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'default-key', 'salt', 32);
            
            const decipher = crypto.createDecipher(algorithm, key);
            decipher.setAAD(Buffer.from('additional-data'));
            decipher.setAuthTag(Buffer.from(authTag, 'hex'));
            
            let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;

        } catch (error) {
            console.error('Error decrypting data:', error);
            throw error;
        }
    }

    // تسجيل محاولات الدخول المشبوهة
    static async logSuspiciousActivity(userId, activity, ipAddress, userAgent) {
        try {
            await db.run(`
                INSERT INTO security_logs (
                    user_id, activity_type, ip_address, user_agent, 
                    created_at, is_suspicious
                ) VALUES (?, ?, ?, ?, datetime('now'), 1)
            `, [userId, activity, ipAddress, userAgent]);

            // إرسال تنبيه للمدراء
            const NotificationService = require('./notificationService');
            await NotificationService.createRoleNotification(['admin'], {
                title: 'نشاط مشبوه',
                message: `تم رصد نشاط مشبوه من المستخدم ${userId}: ${activity}`,
                type: 'warning',
                priority: 'high',
                entityType: 'security_alert',
                entityId: userId
            });

        } catch (error) {
            console.error('Error logging suspicious activity:', error);
        }
    }

    // فحص قوة كلمة المرور
    static checkPasswordStrength(password) {
        const checks = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            numbers: /\d/.test(password),
            symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password),
            noCommon: !this.isCommonPassword(password)
        };

        const score = Object.values(checks).filter(Boolean).length;
        
        let strength = 'ضعيف';
        if (score >= 5) strength = 'قوي';
        else if (score >= 3) strength = 'متوسط';

        return {
            score,
            strength,
            checks,
            suggestions: this.getPasswordSuggestions(checks)
        };
    }

    // التحقق من كلمات المرور الشائعة
    static isCommonPassword(password) {
        const commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        return commonPasswords.includes(password.toLowerCase());
    }

    // اقتراحات تحسين كلمة المرور
    static getPasswordSuggestions(checks) {
        const suggestions = [];
        
        if (!checks.length) suggestions.push('استخدم 8 أحرف على الأقل');
        if (!checks.uppercase) suggestions.push('أضف حروف كبيرة');
        if (!checks.lowercase) suggestions.push('أضف حروف صغيرة');
        if (!checks.numbers) suggestions.push('أضف أرقام');
        if (!checks.symbols) suggestions.push('أضف رموز خاصة');
        if (!checks.noCommon) suggestions.push('تجنب كلمات المرور الشائعة');
        
        return suggestions;
    }

    // إنشاء رمز استرداد كلمة المرور
    static async generatePasswordResetToken(email) {
        try {
            const user = await db.get(
                'SELECT id FROM users WHERE email = ?',
                [email]
            );

            if (!user) {
                throw new Error('User not found');
            }

            const token = crypto.randomBytes(32).toString('hex');
            const expiresAt = new Date(Date.now() + 3600000); // ساعة واحدة

            await db.run(`
                INSERT OR REPLACE INTO password_reset_tokens (
                    user_id, token, expires_at, created_at
                ) VALUES (?, ?, ?, datetime('now'))
            `, [user.id, token, expiresAt.toISOString()]);

            return token;

        } catch (error) {
            console.error('Error generating password reset token:', error);
            throw error;
        }
    }

    // التحقق من رمز استرداد كلمة المرور
    static async verifyPasswordResetToken(token) {
        try {
            const resetToken = await db.get(`
                SELECT user_id, expires_at 
                FROM password_reset_tokens 
                WHERE token = ? AND expires_at > datetime('now')
            `, [token]);

            if (!resetToken) {
                throw new Error('Invalid or expired token');
            }

            return resetToken.user_id;

        } catch (error) {
            console.error('Error verifying password reset token:', error);
            throw error;
        }
    }

    // إعادة تعيين كلمة المرور
    static async resetPassword(token, newPassword) {
        try {
            const userId = await this.verifyPasswordResetToken(token);
            
            // التحقق من قوة كلمة المرور الجديدة
            const passwordStrength = this.checkPasswordStrength(newPassword);
            if (passwordStrength.score < 3) {
                throw new Error('Password is too weak');
            }

            // تشفير كلمة المرور الجديدة
            const hashedPassword = await bcrypt.hash(newPassword, 12);

            // تحديث كلمة المرور
            await db.run(
                'UPDATE users SET password = ? WHERE id = ?',
                [hashedPassword, userId]
            );

            // حذف رمز الاسترداد
            await db.run(
                'DELETE FROM password_reset_tokens WHERE token = ?',
                [token]
            );

            // تسجيل النشاط
            await this.logSecurityEvent(userId, 'password_reset', 'Password reset successfully');

            return { success: true };

        } catch (error) {
            console.error('Error resetting password:', error);
            throw error;
        }
    }

    // تسجيل الأحداث الأمنية
    static async logSecurityEvent(userId, eventType, description, ipAddress = null) {
        try {
            await db.run(`
                INSERT INTO security_logs (
                    user_id, activity_type, description, ip_address, 
                    created_at, is_suspicious
                ) VALUES (?, ?, ?, ?, datetime('now'), 0)
            `, [userId, eventType, description, ipAddress]);

        } catch (error) {
            console.error('Error logging security event:', error);
        }
    }

    // فحص محاولات الدخول المتعددة
    static async checkBruteForceAttempts(email, ipAddress) {
        try {
            const attempts = await db.get(`
                SELECT COUNT(*) as count 
                FROM security_logs 
                WHERE (user_id = (SELECT id FROM users WHERE email = ?) OR ip_address = ?)
                AND activity_type = 'failed_login'
                AND created_at > datetime('now', '-15 minutes')
            `, [email, ipAddress]);

            return attempts.count >= 5; // 5 محاولات في 15 دقيقة

        } catch (error) {
            console.error('Error checking brute force attempts:', error);
            return false;
        }
    }

    // إنشاء جلسة آمنة
    static async createSecureSession(userId, ipAddress, userAgent) {
        try {
            const sessionToken = crypto.randomBytes(32).toString('hex');
            const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 ساعة

            await db.run(`
                INSERT INTO user_sessions (
                    user_id, session_token, ip_address, user_agent,
                    expires_at, created_at
                ) VALUES (?, ?, ?, ?, ?, datetime('now'))
            `, [userId, sessionToken, ipAddress, userAgent, expiresAt.toISOString()]);

            return sessionToken;

        } catch (error) {
            console.error('Error creating secure session:', error);
            throw error;
        }
    }

    // التحقق من الجلسة
    static async verifySession(sessionToken) {
        try {
            const session = await db.get(`
                SELECT s.user_id, u.email, u.role, u.is_active
                FROM user_sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.session_token = ? 
                AND s.expires_at > datetime('now')
                AND u.is_active = 1
            `, [sessionToken]);

            return session || null;

        } catch (error) {
            console.error('Error verifying session:', error);
            return null;
        }
    }
}

module.exports = SecurityService;
