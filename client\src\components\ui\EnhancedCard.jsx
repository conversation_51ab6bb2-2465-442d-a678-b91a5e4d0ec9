import { forwardRef } from 'react'

const EnhancedCard = forwardRef(({
  children,
  className = '',
  clickable = false,
  hover = true,
  padding = 'p-6',
  onClick,
  ...props
}, ref) => {
  const classes = [
    'enhanced-card',
    padding,
    clickable ? 'clickable' : '',
    className
  ].filter(Boolean).join(' ')

  return (
    <div
      ref={ref}
      className={classes}
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  )
})

EnhancedCard.displayName = 'EnhancedCard'

export default EnhancedCard
