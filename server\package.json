{"name": "construction-erp-server", "version": "1.0.0", "description": "Backend API for Construction ERP System", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "node-cron": "^3.0.3", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "sequelize": "^6.35.2", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "pdfkit": "^0.14.0", "exceljs": "^4.4.0", "moment": "^2.29.4", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}