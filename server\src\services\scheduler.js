// خدمة الجدولة التلقائية
const cron = require('node-cron');
const NotificationService = require('./notificationService');

class SchedulerService {
    static init() {
        console.log('🕐 تم تفعيل خدمة الجدولة التلقائية');

        // فحص الفواتير المستحقة كل ساعة
        cron.schedule('0 * * * *', async () => {
            try {
                console.log('🔍 فحص الفواتير المستحقة...');
                await NotificationService.checkOverdueInvoices();
            } catch (error) {
                console.error('خطأ في فحص الفواتير المستحقة:', error);
            }
        });

        // فحص المخزون المنخفض كل 6 ساعات
        cron.schedule('0 */6 * * *', async () => {
            try {
                console.log('🔍 فحص المخزون المنخفض...');
                await NotificationService.checkLowStock();
            } catch (error) {
                console.error('خطأ في فحص المخزون:', error);
            }
        });

        // فحص المشاريع المتأخرة يومياً في الساعة 9 صباحاً
        cron.schedule('0 9 * * *', async () => {
            try {
                console.log('🔍 فحص المشاريع المتأخرة...');
                await NotificationService.checkDelayedProjects();
            } catch (error) {
                console.error('خطأ في فحص المشاريع المتأخرة:', error);
            }
        });

        // فحص تجاوز الميزانيات يومياً في الساعة 10 صباحاً
        cron.schedule('0 10 * * *', async () => {
            try {
                console.log('🔍 فحص تجاوز الميزانيات...');
                await NotificationService.checkBudgetOverruns();
            } catch (error) {
                console.error('خطأ في فحص تجاوز الميزانيات:', error);
            }
        });

        // تنظيف الإشعارات القديمة أسبوعياً
        cron.schedule('0 2 * * 0', async () => {
            try {
                console.log('🧹 تنظيف الإشعارات القديمة...');
                await this.cleanupOldNotifications();
            } catch (error) {
                console.error('خطأ في تنظيف الإشعارات:', error);
            }
        });

        console.log('✅ تم تفعيل جميع المهام المجدولة');
    }

    // تنظيف الإشعارات القديمة (أكثر من 30 يوم)
    static async cleanupOldNotifications() {
        const db = require('../database/connection');
        
        try {
            const result = await db.run(`
                DELETE FROM notifications 
                WHERE created_at < date('now', '-30 days')
                AND is_read = 1
            `);

            console.log(`🗑️ تم حذف ${result.changes} إشعار قديم`);
            return result.changes;
        } catch (error) {
            console.error('خطأ في تنظيف الإشعارات:', error);
            throw error;
        }
    }

    // تشغيل فحص فوري لجميع الإشعارات
    static async runImmediateCheck() {
        try {
            console.log('🚀 تشغيل فحص فوري للإشعارات...');
            
            const results = await NotificationService.runAutomaticChecks();
            
            console.log('✅ انتهى الفحص الفوري:', results);
            return results;
        } catch (error) {
            console.error('خطأ في الفحص الفوري:', error);
            throw error;
        }
    }
}

module.exports = SchedulerService;
