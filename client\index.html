<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🏗️ نظام إدارة المقاولات المتقدم</title>
    <meta name="description" content="نظام محاسبي شامل لإدارة المقاولات مع الامتثال الضريبي المصري والذكاء الاصطناعي والتسوية التلقائية" />
    <meta name="theme-color" content="#2563eb" />
    <meta name="author" content="نظام إدارة المقاولات" />
    <meta name="keywords" content="محاسبة, مقاولات, إدارة مشاريع, ضرائب مصرية, ERP" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />

    <!-- Loading Animation -->
    <style>
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: 'Cairo', sans-serif;
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 10px;
        animation: pulse 2s ease-in-out infinite;
      }

      .loading-subtitle {
        font-size: 1rem;
        opacity: 0.8;
        text-align: center;
        max-width: 300px;
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">🏗️ نظام إدارة المقاولات</div>
      <div class="loading-subtitle">جاري تحميل النظام المحاسبي المتقدم...</div>
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>

    <!-- Remove loading screen when app loads -->
    <script>
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => loading.remove(), 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
