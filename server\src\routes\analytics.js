const express = require('express');
const { authMiddleware } = require('../middleware/auth');
const AdvancedAnalyticsService = require('../services/advancedAnalytics');

const router = express.Router();

// الحصول على الاتجاهات المالية
router.get('/financial-trends', authMiddleware, async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        if (!startDate || !endDate) {
            return res.status(400).json({
                success: false,
                message: 'Start date and end date are required'
            });
        }

        const trends = await AdvancedAnalyticsService.getFinancialTrends(startDate, endDate);

        res.json({
            success: true,
            data: trends
        });

    } catch (error) {
        console.error('Get financial trends error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحليل أداء المشاريع المتقدم
router.get('/project-performance', authMiddleware, async (req, res) => {
    try {
        const analysis = await AdvancedAnalyticsService.getProjectPerformanceAnalysis();

        res.json({
            success: true,
            data: analysis
        });

    } catch (error) {
        console.error('Get project performance analysis error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحليل التدفق النقدي المتقدم
router.get('/cash-flow', authMiddleware, async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        if (!startDate || !endDate) {
            return res.status(400).json({
                success: false,
                message: 'Start date and end date are required'
            });
        }

        const analysis = await AdvancedAnalyticsService.getCashFlowAnalysis(startDate, endDate);

        res.json({
            success: true,
            data: analysis
        });

    } catch (error) {
        console.error('Get cash flow analysis error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحليل العملاء والموردين
router.get('/customer-supplier', authMiddleware, async (req, res) => {
    try {
        const analysis = await AdvancedAnalyticsService.getCustomerSupplierAnalysis();

        res.json({
            success: true,
            data: analysis
        });

    } catch (error) {
        console.error('Get customer supplier analysis error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحليل المخزون المتقدم
router.get('/inventory', authMiddleware, async (req, res) => {
    try {
        const analysis = await AdvancedAnalyticsService.getInventoryAnalysis();

        res.json({
            success: true,
            data: analysis
        });

    } catch (error) {
        console.error('Get inventory analysis error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحليل الربحية
router.get('/profitability', authMiddleware, async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        if (!startDate || !endDate) {
            return res.status(400).json({
                success: false,
                message: 'Start date and end date are required'
            });
        }

        const analysis = await AdvancedAnalyticsService.getProfitabilityAnalysis(startDate, endDate);

        res.json({
            success: true,
            data: analysis
        });

    } catch (error) {
        console.error('Get profitability analysis error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// لوحة التحكم الشاملة
router.get('/comprehensive-dashboard', authMiddleware, async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        if (!startDate || !endDate) {
            return res.status(400).json({
                success: false,
                message: 'Start date and end date are required'
            });
        }

        const dashboard = await AdvancedAnalyticsService.getComprehensiveDashboard(startDate, endDate);

        res.json({
            success: true,
            data: dashboard
        });

    } catch (error) {
        console.error('Get comprehensive dashboard error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحليل مخصص
router.post('/custom-analysis', authMiddleware, async (req, res) => {
    try {
        const { 
            analysisType, 
            parameters, 
            startDate, 
            endDate,
            filters 
        } = req.body;

        let result;

        switch (analysisType) {
            case 'financial_trends':
                result = await AdvancedAnalyticsService.getFinancialTrends(startDate, endDate);
                break;
            case 'project_performance':
                result = await AdvancedAnalyticsService.getProjectPerformanceAnalysis();
                break;
            case 'cash_flow':
                result = await AdvancedAnalyticsService.getCashFlowAnalysis(startDate, endDate);
                break;
            case 'profitability':
                result = await AdvancedAnalyticsService.getProfitabilityAnalysis(startDate, endDate);
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: 'Invalid analysis type'
                });
        }

        res.json({
            success: true,
            data: result,
            analysisType,
            parameters: { startDate, endDate, filters }
        });

    } catch (error) {
        console.error('Custom analysis error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// إحصائيات سريعة
router.get('/quick-stats', authMiddleware, async (req, res) => {
    try {
        const db = require('../database/connection');
        
        // إحصائيات سريعة
        const stats = await Promise.all([
            // إجمالي الإيرادات هذا الشهر
            db.get(`
                SELECT COALESCE(SUM(amount), 0) as total 
                FROM accounting_entries 
                WHERE type = 'revenue' 
                AND DATE(created_at) >= DATE('now', 'start of month')
            `),
            // إجمالي المصروفات هذا الشهر
            db.get(`
                SELECT COALESCE(SUM(amount), 0) as total 
                FROM accounting_entries 
                WHERE type = 'expense' 
                AND DATE(created_at) >= DATE('now', 'start of month')
            `),
            // عدد المشاريع النشطة
            db.get(`
                SELECT COUNT(*) as total 
                FROM projects 
                WHERE status = 'active'
            `),
            // عدد الفواتير المستحقة
            db.get(`
                SELECT COUNT(*) as total 
                FROM invoices 
                WHERE due_date < DATE('now') 
                AND status NOT IN ('paid', 'cancelled')
            `),
            // عدد الأصناف منخفضة المخزون
            db.get(`
                SELECT COUNT(*) as total 
                FROM inventory_items 
                WHERE current_stock <= minimum_stock 
                AND is_active = 1
            `)
        ]);

        const quickStats = {
            monthlyRevenue: stats[0].total,
            monthlyExpenses: stats[1].total,
            monthlyProfit: stats[0].total - stats[1].total,
            activeProjects: stats[2].total,
            overdueInvoices: stats[3].total,
            lowStockItems: stats[4].total
        };

        res.json({
            success: true,
            data: quickStats
        });

    } catch (error) {
        console.error('Get quick stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
