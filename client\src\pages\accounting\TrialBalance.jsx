import { useState, useEffect } from 'react'

function TrialBalance() {
  const [balanceData, setBalanceData] = useState([])
  const [isBalanced, setIsBalanced] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('2024-01')
  const [showUnbalancedOnly, setShowUnbalancedOnly] = useState(false)

  // بيانات ميزان المراجعة
  const trialBalanceData = [
    { code: '1110', name: 'النقدية بالصندوق', debit: 48000, credit: 0, type: 'asset' },
    { code: '1120', name: 'النقدية بالبنوك', debit: 282000, credit: 0, type: 'asset' },
    { code: '1130', name: 'حسابات العملاء', debit: 200000, credit: 0, type: 'asset' },
    { code: '1140', name: 'أوراق القبض', debit: 75000, credit: 0, type: 'asset' },
    { code: '1150', name: 'المخزون - مواد خام', debit: 120000, credit: 0, type: 'asset' },
    { code: '1160', name: 'المخزون - مواد تحت التشغيل', debit: 85000, credit: 0, type: 'asset' },
    { code: '1210', name: 'الأراضي والمباني', debit: 500000, credit: 0, type: 'asset' },
    { code: '1220', name: 'المعدات والآلات', debit: 300000, credit: 0, type: 'asset' },
    { code: '1230', name: 'وسائل النقل', debit: 150000, credit: 0, type: 'asset' },
    { code: '1240', name: 'مجمع الإهلاك', debit: 0, credit: 80000, type: 'asset' },
    
    { code: '2110', name: 'حسابات الموردين', debit: 0, credit: 95000, type: 'liability' },
    { code: '2120', name: 'أوراق الدفع', debit: 0, credit: 45000, type: 'liability' },
    { code: '2130', name: 'مصروفات مستحقة', debit: 0, credit: 25000, type: 'liability' },
    { code: '2140', name: 'ضرائب مستحقة', debit: 0, credit: 35000, type: 'liability' },
    { code: '2150', name: 'رواتب مستحقة', debit: 0, credit: 40000, type: 'liability' },
    
    { code: '3100', name: 'رأس المال', debit: 0, credit: 500000, type: 'equity' },
    { code: '3200', name: 'الاحتياطيات', debit: 0, credit: 100000, type: 'equity' },
    { code: '3300', name: 'الأرباح المحتجزة', debit: 0, credit: 150000, type: 'equity' },
    
    { code: '4100', name: 'إيرادات المشاريع', debit: 0, credit: 750000, type: 'revenue' },
    { code: '4200', name: 'إيرادات أخرى', debit: 0, credit: 50000, type: 'revenue' },
    { code: '4300', name: 'إيرادات استثمارية', debit: 0, credit: 25000, type: 'revenue' },
    
    { code: '5110', name: 'مواد البناء', debit: 300000, credit: 0, type: 'expense' },
    { code: '5120', name: 'أجور العمال', debit: 150000, credit: 0, type: 'expense' },
    { code: '5130', name: 'معدات وآلات', debit: 80000, credit: 0, type: 'expense' },
    { code: '5210', name: 'رواتب إدارية', debit: 60000, credit: 0, type: 'expense' },
    { code: '5220', name: 'إيجارات', debit: 24000, credit: 0, type: 'expense' },
    { code: '5230', name: 'مصروفات تسويق', debit: 15000, credit: 0, type: 'expense' },
    { code: '5240', name: 'مصروفات عمومية', debit: 12000, credit: 0, type: 'expense' }
  ]

  useEffect(() => {
    setBalanceData(trialBalanceData)
    checkBalance()
  }, [])

  const checkBalance = () => {
    const totalDebit = trialBalanceData.reduce((sum, item) => sum + item.debit, 0)
    const totalCredit = trialBalanceData.reduce((sum, item) => sum + item.credit, 0)
    setIsBalanced(totalDebit === totalCredit)
  }

  const getTotalDebit = () => balanceData.reduce((sum, item) => sum + item.debit, 0)
  const getTotalCredit = () => balanceData.reduce((sum, item) => sum + item.credit, 0)
  const getDifference = () => Math.abs(getTotalDebit() - getTotalCredit())

  const getAccountTypeColor = (type) => {
    switch (type) {
      case 'asset': return 'text-green-600'
      case 'liability': return 'text-red-600'
      case 'equity': return 'text-blue-600'
      case 'revenue': return 'text-yellow-600'
      case 'expense': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  const getAccountTypeIcon = (type) => {
    switch (type) {
      case 'asset': return '💰'
      case 'liability': return '📋'
      case 'equity': return '🏛️'
      case 'revenue': return '📈'
      case 'expense': return '📉'
      default: return '📄'
    }
  }

  const filteredData = showUnbalancedOnly 
    ? balanceData.filter(item => item.debit !== item.credit)
    : balanceData

  const handleCorrection = (accountCode) => {
    alert(`🔧 فتح نافذة تصحيح الحساب: ${accountCode}\n\nسيتم إضافة قيد تسوية لتصحيح الرصيد`)
  }

  const generateAdjustmentEntry = () => {
    if (isBalanced) {
      alert('✅ الميزان متوازن - لا حاجة لقيود تسوية')
      return
    }
    
    const difference = getDifference()
    alert(`🔧 إنشاء قيد تسوية بقيمة ${difference.toLocaleString('ar-EG')} ج.م\n\nسيتم إضافة القيد تلقائياً لتوازن الميزان`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                ⚖️ ميزان المراجعة التفاعلي
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="2024-01">يناير 2024</option>
                <option value="2024-02">فبراير 2024</option>
                <option value="2024-03">مارس 2024</option>
              </select>
              <button
                onClick={() => alert('📊 جاري تصدير ميزان المراجعة...')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📋 تصدير PDF
              </button>
              {!isBalanced && (
                <button
                  onClick={generateAdjustmentEntry}
                  className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  🔧 قيد تسوية
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* حالة التوازن */}
        <div className={`mb-6 p-4 rounded-lg border-2 ${
          isBalanced 
            ? 'bg-green-50 border-green-200' 
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`text-2xl mr-3 ${isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                {isBalanced ? '✅' : '⚠️'}
              </div>
              <div>
                <h3 className={`font-medium ${isBalanced ? 'text-green-800' : 'text-red-800'}`}>
                  {isBalanced ? 'الميزان متوازن' : 'الميزان غير متوازن'}
                </h3>
                <p className={`text-sm ${isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                  {isBalanced 
                    ? 'جميع الحسابات متوازنة والمدين يساوي الدائن'
                    : `يوجد فرق قدره ${getDifference().toLocaleString('ar-EG')} ج.م`
                  }
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="text-sm text-gray-500">إجمالي المدين</div>
                <div className="text-lg font-bold text-green-600">
                  {getTotalDebit().toLocaleString('ar-EG')} ج.م
                </div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-500">إجمالي الدائن</div>
                <div className="text-lg font-bold text-red-600">
                  {getTotalCredit().toLocaleString('ar-EG')} ج.م
                </div>
              </div>
              {!isBalanced && (
                <div className="text-center">
                  <div className="text-sm text-gray-500">الفرق</div>
                  <div className="text-lg font-bold text-orange-600">
                    {getDifference().toLocaleString('ar-EG')} ج.م
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* فلاتر وخيارات */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={showUnbalancedOnly}
                  onChange={(e) => setShowUnbalancedOnly(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">عرض الحسابات غير المتوازنة فقط</span>
              </label>
            </div>
            
            <div className="text-sm text-gray-500">
              عدد الحسابات: {filteredData.length} | 
              آخر تحديث: {new Date().toLocaleDateString('ar-EG')}
            </div>
          </div>
        </div>

        {/* جدول ميزان المراجعة */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              📊 ميزان المراجعة - {selectedPeriod}
            </h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    رمز الحساب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    اسم الحساب
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    النوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    مدين
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    دائن
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    الرصيد
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                    إجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredData.map((account, index) => {
                  const balance = account.debit - account.credit
                  const isUnbalanced = account.debit !== 0 && account.credit !== 0
                  
                  return (
                    <tr key={account.code} className={`hover:bg-gray-50 ${isUnbalanced ? 'bg-yellow-50' : ''}`}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                        {account.code}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="flex items-center">
                          <span className="mr-2">{getAccountTypeIcon(account.type)}</span>
                          {account.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm font-medium ${getAccountTypeColor(account.type)}`}>
                          {account.type === 'asset' ? 'أصول' :
                           account.type === 'liability' ? 'خصوم' :
                           account.type === 'equity' ? 'حقوق ملكية' :
                           account.type === 'revenue' ? 'إيرادات' : 'مصروفات'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                        {account.debit > 0 ? account.debit.toLocaleString('ar-EG') : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                        {account.credit > 0 ? account.credit.toLocaleString('ar-EG') : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <span className={balance >= 0 ? 'text-green-600' : 'text-red-600'}>
                          {Math.abs(balance).toLocaleString('ar-EG')} 
                          {balance >= 0 ? ' مدين' : ' دائن'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button 
                          onClick={() => alert(`عرض كشف حساب: ${account.name}`)}
                          className="text-blue-600 hover:text-blue-900 ml-4"
                        >
                          عرض
                        </button>
                        {isUnbalanced && (
                          <button 
                            onClick={() => handleCorrection(account.code)}
                            className="text-orange-600 hover:text-orange-900"
                          >
                            تصحيح
                          </button>
                        )}
                      </td>
                    </tr>
                  )
                })}
              </tbody>
              
              {/* إجمالي */}
              <tfoot className="bg-gray-100">
                <tr>
                  <td colSpan="3" className="px-6 py-4 text-sm font-bold text-gray-900">
                    الإجمالي
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                    {getTotalDebit().toLocaleString('ar-EG')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-red-600">
                    {getTotalCredit().toLocaleString('ar-EG')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold">
                    <span className={isBalanced ? 'text-green-600' : 'text-red-600'}>
                      {isBalanced ? 'متوازن' : `فرق: ${getDifference().toLocaleString('ar-EG')}`}
                    </span>
                  </td>
                  <td className="px-6 py-4"></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        {/* تحليل سريع */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-center">
              <div className="text-2xl mb-2">💰</div>
              <div className="text-sm text-gray-500">إجمالي الأصول</div>
              <div className="text-lg font-bold text-green-600">
                {filteredData
                  .filter(a => a.type === 'asset')
                  .reduce((sum, a) => sum + a.debit - a.credit, 0)
                  .toLocaleString('ar-EG')} ج.م
              </div>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-center">
              <div className="text-2xl mb-2">📋</div>
              <div className="text-sm text-gray-500">إجمالي الخصوم</div>
              <div className="text-lg font-bold text-red-600">
                {filteredData
                  .filter(a => a.type === 'liability')
                  .reduce((sum, a) => sum + a.credit - a.debit, 0)
                  .toLocaleString('ar-EG')} ج.م
              </div>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-center">
              <div className="text-2xl mb-2">📈</div>
              <div className="text-sm text-gray-500">إجمالي الإيرادات</div>
              <div className="text-lg font-bold text-yellow-600">
                {filteredData
                  .filter(a => a.type === 'revenue')
                  .reduce((sum, a) => sum + a.credit - a.debit, 0)
                  .toLocaleString('ar-EG')} ج.م
              </div>
            </div>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-center">
              <div className="text-2xl mb-2">📉</div>
              <div className="text-sm text-gray-500">إجمالي المصروفات</div>
              <div className="text-lg font-bold text-purple-600">
                {filteredData
                  .filter(a => a.type === 'expense')
                  .reduce((sum, a) => sum + a.debit - a.credit, 0)
                  .toLocaleString('ar-EG')} ج.م
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TrialBalance
