# Construction ERP System

A comprehensive construction accounting software with Egyptian tax compliance and AI capabilities.

## Features

### Core Modules
- **General Accounting**: Chart of accounts, general ledger, trial balance, income statement
- **Treasury & Banking**: Cash/bank transactions, receipts/payments with cash flow prediction
- **Accounts Receivable/Payable**: Invoicing, payments, aging reports
- **Inventory Management**: Receiving, issuing, stock alerts with consumption analysis
- **Project Management**: Project registration, cost tracking with predictive analysis
- **Human Resources**: Attendance, payroll, advances, custody
- **Tax & Compliance**: Egyptian VAT, income tax, withholding tax, Form 41
- **AI Analytics**: Performance monitoring, risk analysis, recommendations

### Technical Stack
- **Frontend**: React 18 + Vite + Tailwind CSS
- **Backend**: Node.js + Express + SQLite/PostgreSQL
- **AI Components**: Python integration for analytics
- **Authentication**: JWT-based role-based access control
- **Export**: PDF/Excel generation

## Quick Start

1. Install dependencies:
```bash
npm run install:all
```

2. Start development servers:
```bash
npm run dev
```

3. Access the application:
- Frontend: http://localhost:5173
- Backend API: http://localhost:3001

## Project Structure

```
construction-erp/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── modules/        # Business modules
│   │   ├── services/       # API services
│   │   ├── utils/          # Utilities
│   │   └── styles/         # CSS/Tailwind styles
├── server/                 # Node.js backend
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── models/         # Database models
│   │   ├── middleware/     # Express middleware
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utilities
├── ai-analytics/           # Python AI components
│   ├── models/             # ML models
│   ├── services/           # AI services
│   └── api/                # AI API endpoints
└── docs/                   # Documentation
```

## Egyptian Tax Compliance

The system includes comprehensive Egyptian tax compliance features:
- VAT calculations and reporting
- Withholding tax management
- Income tax calculations
- Form 41 generation
- Tax audit trails

## AI Analytics

AI-powered features include:
- Cash flow prediction
- Cost overrun analysis
- Payment delay prediction
- Resource optimization
- Risk assessment
- Performance recommendations

## License

MIT License
