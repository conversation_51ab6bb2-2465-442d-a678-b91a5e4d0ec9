# Dependencies
node_modules/
*/node_modules/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Configuration files with sensitive data
config/production.json
config/staging.json
.secrets
secrets.json
credentials.json
service-account-key.json

# Database
*.db
*.sqlite
*.sqlite3
*.mdb
*.accdb
*.dbf
*.fdb
*.gdb
*.mdf
*.ldf
*.bak
*.sql.gz
*.sql.bz2
database/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Development and testing
.nyc_output/
test-results/
junit.xml
.jest/
cypress/videos/
cypress/screenshots/
playwright-report/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Uploads and temporary files
uploads/
temp/
tmp/

# Backups
backups/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# AI/ML specific
*.h5
*.hdf5
*.pkl
*.pickle
*.joblib
*.model
*.weights
*.ckpt
*.pb
ai-analytics/models/
checkpoints/
tensorboard_logs/
mlruns/
.mlflow/
wandb/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Build outputs
build/
dist/
