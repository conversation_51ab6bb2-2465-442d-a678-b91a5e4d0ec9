// خدمة التحليلات الذكية والذكاء الاصطناعي
const db = require('../database/connection');

class AIAnalyticsService {
    // توقع التدفق النقدي
    static async predictCashFlow(projectId = null, daysAhead = 30) {
        try {
            // الحصول على البيانات التاريخية
            const historicalData = await this.getHistoricalCashFlow(projectId, 90);
            
            // حساب المتوسطات والاتجاهات
            const analysis = this.analyzeCashFlowTrends(historicalData);
            
            // إنشاء التوقع
            const prediction = {
                prediction_type: 'cash_flow',
                entity_type: projectId ? 'project' : 'company',
                entity_id: projectId,
                prediction_data: JSON.stringify({
                    predicted_inflow: analysis.avgInflow * (daysAhead / 30),
                    predicted_outflow: analysis.avgOutflow * (daysAhead / 30),
                    net_cash_flow: (analysis.avgInflow - analysis.avgOutflow) * (daysAhead / 30),
                    confidence_factors: analysis.confidenceFactors,
                    trend_direction: analysis.trend,
                    risk_level: analysis.riskLevel
                }),
                confidence_score: analysis.confidence,
                prediction_date: new Date().toISOString().split('T')[0]
            };
            
            // حفظ التوقع
            await this.savePrediction(prediction);
            
            return prediction;
        } catch (error) {
            console.error('Error predicting cash flow:', error);
            throw error;
        }
    }
    
    // توقع تجاوز تكلفة المشروع
    static async predictCostOverrun(projectId) {
        try {
            // الحصول على بيانات المشروع
            const project = await db.get(`
                SELECT p.*, 
                       COALESCE(SUM(pt.amount), 0) as actual_spent,
                       COUNT(pt.id) as transaction_count
                FROM projects p
                LEFT JOIN project_transactions pt ON p.id = pt.project_id
                WHERE p.id = ?
                GROUP BY p.id
            `, [projectId]);
            
            if (!project) {
                throw new Error('Project not found');
            }
            
            // حساب نسبة الإنجاز المقدرة
            const daysElapsed = this.calculateDaysElapsed(project.start_date);
            const totalProjectDays = this.calculateProjectDuration(project.start_date, project.end_date);
            const progressPercentage = Math.min(daysElapsed / totalProjectDays, 1);
            
            // حساب معدل الإنفاق
            const spendRate = project.actual_spent / Math.max(daysElapsed, 1);
            const projectedTotalCost = spendRate * totalProjectDays;
            
            // تحليل المخاطر
            const overrunRisk = this.calculateOverrunRisk(project, projectedTotalCost, progressPercentage);
            
            const prediction = {
                prediction_type: 'cost_overrun',
                entity_type: 'project',
                entity_id: projectId,
                prediction_data: JSON.stringify({
                    estimated_budget: project.estimated_budget,
                    actual_spent: project.actual_spent,
                    projected_total_cost: projectedTotalCost,
                    potential_overrun: Math.max(0, projectedTotalCost - project.estimated_budget),
                    overrun_percentage: ((projectedTotalCost - project.estimated_budget) / project.estimated_budget * 100),
                    progress_percentage: progressPercentage * 100,
                    risk_factors: overrunRisk.factors,
                    recommendations: overrunRisk.recommendations
                }),
                confidence_score: overrunRisk.confidence,
                prediction_date: new Date().toISOString().split('T')[0]
            };
            
            await this.savePrediction(prediction);
            
            // إنشاء توصية إذا كان هناك خطر تجاوز
            if (overrunRisk.level === 'high') {
                await this.createRecommendation({
                    type: 'risk_mitigation',
                    title: `خطر تجاوز ميزانية المشروع ${project.project_name}`,
                    description: `المشروع معرض لتجاوز الميزانية بنسبة ${overrunRisk.overrunPercentage.toFixed(1)}%`,
                    priority: 'high',
                    estimated_impact: overrunRisk.potentialOverrun,
                    entity_type: 'project',
                    entity_id: projectId
                });
            }
            
            return prediction;
        } catch (error) {
            console.error('Error predicting cost overrun:', error);
            throw error;
        }
    }
    
    // توقع تأخير الدفع
    static async predictPaymentDelay(customerId = null) {
        try {
            // الحصول على تاريخ الدفعات
            const paymentHistory = await db.all(`
                SELECT 
                    i.id,
                    i.invoice_date,
                    i.due_date,
                    i.total_amount,
                    i.paid_amount,
                    i.status,
                    c.customer_name,
                    c.payment_terms,
                    JULIANDAY(COALESCE(p.payment_date, date('now'))) - JULIANDAY(i.due_date) as days_late
                FROM invoices i
                JOIN customers c ON i.customer_id = c.id
                LEFT JOIN (
                    SELECT pa.invoice_id, MIN(p.payment_date) as payment_date
                    FROM payment_allocations pa
                    JOIN payments p ON pa.payment_id = p.id
                    GROUP BY pa.invoice_id
                ) p ON i.id = p.invoice_id
                WHERE i.invoice_type = 'sales'
                ${customerId ? 'AND c.id = ?' : ''}
                ORDER BY i.invoice_date DESC
                LIMIT 100
            `, customerId ? [customerId] : []);
            
            // تحليل أنماط التأخير
            const delayAnalysis = this.analyzePaymentDelays(paymentHistory);
            
            const prediction = {
                prediction_type: 'payment_delay',
                entity_type: customerId ? 'customer' : 'company',
                entity_id: customerId,
                prediction_data: JSON.stringify({
                    average_delay_days: delayAnalysis.avgDelay,
                    delay_probability: delayAnalysis.delayProbability,
                    risk_factors: delayAnalysis.riskFactors,
                    seasonal_patterns: delayAnalysis.seasonalPatterns,
                    customer_risk_score: delayAnalysis.riskScore
                }),
                confidence_score: delayAnalysis.confidence,
                prediction_date: new Date().toISOString().split('T')[0]
            };
            
            await this.savePrediction(prediction);
            
            return prediction;
        } catch (error) {
            console.error('Error predicting payment delay:', error);
            throw error;
        }
    }
    
    // تحسين الموارد
    static async optimizeResources() {
        try {
            // تحليل استخدام المخزون
            const inventoryAnalysis = await this.analyzeInventoryUsage();
            
            // تحليل كفاءة المشاريع
            const projectEfficiency = await this.analyzeProjectEfficiency();
            
            // تحليل التكاليف
            const costAnalysis = await this.analyzeCostPatterns();
            
            const recommendations = [];
            
            // توصيات المخزون
            if (inventoryAnalysis.slowMovingItems.length > 0) {
                recommendations.push({
                    type: 'cost_reduction',
                    title: 'تحسين إدارة المخزون',
                    description: `يوجد ${inventoryAnalysis.slowMovingItems.length} صنف بطيء الحركة يمكن تقليل مخزونه`,
                    priority: 'medium',
                    estimated_impact: inventoryAnalysis.potentialSavings
                });
            }
            
            // توصيات المشاريع
            if (projectEfficiency.inefficientProjects.length > 0) {
                recommendations.push({
                    type: 'process_improvement',
                    title: 'تحسين كفاءة المشاريع',
                    description: `${projectEfficiency.inefficientProjects.length} مشروع يحتاج تحسين في الكفاءة`,
                    priority: 'high',
                    estimated_impact: projectEfficiency.potentialImprovement
                });
            }
            
            // حفظ التوصيات
            for (const rec of recommendations) {
                await this.createRecommendation(rec);
            }
            
            const prediction = {
                prediction_type: 'resource_optimization',
                entity_type: 'company',
                entity_id: null,
                prediction_data: JSON.stringify({
                    inventory_optimization: inventoryAnalysis,
                    project_efficiency: projectEfficiency,
                    cost_analysis: costAnalysis,
                    total_potential_savings: inventoryAnalysis.potentialSavings + projectEfficiency.potentialImprovement
                }),
                confidence_score: 0.85,
                prediction_date: new Date().toISOString().split('T')[0]
            };
            
            await this.savePrediction(prediction);
            
            return prediction;
        } catch (error) {
            console.error('Error optimizing resources:', error);
            throw error;
        }
    }
    
    // إنشاء توصية
    static async createRecommendation(recData) {
        const {
            type,
            title,
            description,
            priority = 'medium',
            estimated_impact = 0,
            entity_type = null,
            entity_id = null
        } = recData;
        
        return await db.run(`
            INSERT INTO ai_recommendations (
                recommendation_type, title, description, priority,
                estimated_impact, status, created_at
            ) VALUES (?, ?, ?, ?, ?, 'new', datetime('now'))
        `, [type, title, description, priority, estimated_impact]);
    }
    
    // حفظ توقع
    static async savePrediction(prediction) {
        return await db.run(`
            INSERT INTO ai_predictions (
                prediction_type, entity_type, entity_id, prediction_data,
                confidence_score, prediction_date
            ) VALUES (?, ?, ?, ?, ?, ?)
        `, [
            prediction.prediction_type,
            prediction.entity_type,
            prediction.entity_id,
            prediction.prediction_data,
            prediction.confidence_score,
            prediction.prediction_date
        ]);
    }
    
    // دوال مساعدة
    static async getHistoricalCashFlow(projectId, days) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        
        return await db.all(`
            SELECT 
                DATE(created_at) as date,
                SUM(CASE WHEN debit_amount > 0 THEN debit_amount ELSE 0 END) as inflow,
                SUM(CASE WHEN credit_amount > 0 THEN credit_amount ELSE 0 END) as outflow
            FROM general_ledger
            WHERE created_at >= ?
            ${projectId ? 'AND project_id = ?' : ''}
            GROUP BY DATE(created_at)
            ORDER BY date
        `, projectId ? [startDate.toISOString(), projectId] : [startDate.toISOString()]);
    }
    
    static analyzeCashFlowTrends(data) {
        if (data.length === 0) {
            return {
                avgInflow: 0,
                avgOutflow: 0,
                trend: 'stable',
                confidence: 0.5,
                riskLevel: 'medium',
                confidenceFactors: []
            };
        }
        
        const totalInflow = data.reduce((sum, day) => sum + day.inflow, 0);
        const totalOutflow = data.reduce((sum, day) => sum + day.outflow, 0);
        
        return {
            avgInflow: totalInflow / data.length,
            avgOutflow: totalOutflow / data.length,
            trend: totalInflow > totalOutflow ? 'positive' : 'negative',
            confidence: Math.min(data.length / 30, 1), // أكثر ثقة مع المزيد من البيانات
            riskLevel: totalOutflow > totalInflow * 1.2 ? 'high' : 'medium',
            confidenceFactors: [`${data.length} يوم من البيانات التاريخية`]
        };
    }
    
    static calculateDaysElapsed(startDate) {
        const start = new Date(startDate);
        const now = new Date();
        return Math.max(1, Math.floor((now - start) / (1000 * 60 * 60 * 24)));
    }
    
    static calculateProjectDuration(startDate, endDate) {
        if (!endDate) return 365; // افتراض سنة إذا لم يحدد تاريخ انتهاء
        
        const start = new Date(startDate);
        const end = new Date(endDate);
        return Math.max(1, Math.floor((end - start) / (1000 * 60 * 60 * 24)));
    }
    
    static calculateOverrunRisk(project, projectedCost, progress) {
        const overrunAmount = Math.max(0, projectedCost - project.estimated_budget);
        const overrunPercentage = (overrunAmount / project.estimated_budget) * 100;
        
        let riskLevel = 'low';
        let confidence = 0.7;
        
        if (overrunPercentage > 20) {
            riskLevel = 'high';
            confidence = 0.9;
        } else if (overrunPercentage > 10) {
            riskLevel = 'medium';
            confidence = 0.8;
        }
        
        return {
            level: riskLevel,
            overrunPercentage,
            potentialOverrun: overrunAmount,
            confidence,
            factors: [
                `نسبة الإنجاز: ${(progress * 100).toFixed(1)}%`,
                `نسبة التجاوز المتوقعة: ${overrunPercentage.toFixed(1)}%`
            ],
            recommendations: overrunPercentage > 10 ? [
                'مراجعة ميزانية المشروع',
                'تحسين مراقبة التكاليف',
                'إعادة تقييم النطاق'
            ] : []
        };
    }
    
    static analyzePaymentDelays(paymentHistory) {
        const latePayments = paymentHistory.filter(p => p.days_late > 0);
        const avgDelay = latePayments.length > 0 ? 
            latePayments.reduce((sum, p) => sum + p.days_late, 0) / latePayments.length : 0;
        
        return {
            avgDelay,
            delayProbability: latePayments.length / paymentHistory.length,
            riskScore: Math.min(avgDelay / 30, 1),
            confidence: Math.min(paymentHistory.length / 20, 1),
            riskFactors: [
                `${latePayments.length} من ${paymentHistory.length} دفعة متأخرة`,
                `متوسط التأخير: ${avgDelay.toFixed(1)} يوم`
            ],
            seasonalPatterns: []
        };
    }
    
    static async analyzeInventoryUsage() {
        const slowMovingItems = await db.all(`
            SELECT item_code, item_name, current_stock, unit_cost
            FROM inventory_items
            WHERE current_stock > minimum_stock * 3
            AND is_active = 1
        `);
        
        const potentialSavings = slowMovingItems.reduce((sum, item) => 
            sum + (item.current_stock * item.unit_cost * 0.1), 0);
        
        return {
            slowMovingItems,
            potentialSavings
        };
    }
    
    static async analyzeProjectEfficiency() {
        const inefficientProjects = await db.all(`
            SELECT p.*, 
                   (p.actual_cost / NULLIF(p.estimated_budget, 0)) as cost_ratio
            FROM projects p
            WHERE p.actual_cost > p.estimated_budget * 1.1
            AND p.status IN ('active', 'completed')
        `);
        
        return {
            inefficientProjects,
            potentialImprovement: inefficientProjects.reduce((sum, p) => 
                sum + (p.actual_cost - p.estimated_budget), 0) * 0.2
        };
    }
    
    static async analyzeCostPatterns() {
        return {
            trends: [],
            anomalies: [],
            recommendations: []
        };
    }
}

module.exports = AIAnalyticsService;
