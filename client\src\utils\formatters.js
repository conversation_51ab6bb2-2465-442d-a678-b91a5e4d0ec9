// دوال التنسيق والتحويل

// تنسيق العملة
export const formatCurrency = (amount, currency = 'EGP') => {
  return new Intl.NumberFormat('ar-EG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount || 0)
}

// تنسيق الأرقام
export const formatNumber = (number, decimals = 0) => {
  return new Intl.NumberFormat('ar-EG', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number || 0)
}

// تنسيق النسبة المئوية
export const formatPercentage = (value, decimals = 1) => {
  return new Intl.NumberFormat('ar-EG', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format((value || 0) / 100)
}

// تنسيق التاريخ
export const formatDate = (date, options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  
  return new Intl.DateTimeFormat('ar-EG', { ...defaultOptions, ...options })
    .format(new Date(date))
}

// تنسيق التاريخ المختصر
export const formatShortDate = (date) => {
  return new Intl.DateTimeFormat('ar-EG', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(new Date(date))
}

// تنسيق الوقت
export const formatTime = (date) => {
  return new Intl.DateTimeFormat('ar-EG', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  }).format(new Date(date))
}

// تنسيق التاريخ والوقت
export const formatDateTime = (date) => {
  return new Intl.DateTimeFormat('ar-EG', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  }).format(new Date(date))
}

// حساب الفترة الزمنية
export const getTimeAgo = (date) => {
  const now = new Date()
  const past = new Date(date)
  const diffInSeconds = Math.floor((now - past) / 1000)
  
  if (diffInSeconds < 60) {
    return 'منذ لحظات'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `منذ ${minutes} دقيقة`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `منذ ${hours} ساعة`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `منذ ${days} يوم`
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000)
    return `منذ ${months} شهر`
  } else {
    const years = Math.floor(diffInSeconds / 31536000)
    return `منذ ${years} سنة`
  }
}

// تحويل حالة الفاتورة إلى نص عربي
export const getInvoiceStatusText = (status) => {
  const statusMap = {
    draft: 'مسودة',
    sent: 'مرسلة',
    paid: 'مدفوعة',
    overdue: 'متأخرة',
    cancelled: 'ملغية'
  }
  return statusMap[status] || status
}

// تحويل حالة المشروع إلى نص عربي
export const getProjectStatusText = (status) => {
  const statusMap = {
    planning: 'تخطيط',
    active: 'نشط',
    on_hold: 'متوقف',
    completed: 'مكتمل',
    cancelled: 'ملغي'
  }
  return statusMap[status] || status
}

// تحويل نوع المشروع إلى نص عربي
export const getProjectTypeText = (type) => {
  const typeMap = {
    civil: 'مدني',
    structural: 'إنشائي',
    finishing: 'تشطيبات',
    electrical: 'كهرباء',
    plumbing: 'سباكة',
    infrastructure: 'بنية تحتية'
  }
  return typeMap[type] || type
}

// تحويل نوع الحساب إلى نص عربي
export const getAccountTypeText = (type) => {
  const typeMap = {
    asset: 'أصول',
    liability: 'خصوم',
    equity: 'حقوق ملكية',
    revenue: 'إيرادات',
    expense: 'مصروفات'
  }
  return typeMap[type] || type
}

// تحويل طريقة الدفع إلى نص عربي
export const getPaymentMethodText = (method) => {
  const methodMap = {
    cash: 'نقدي',
    bank_transfer: 'تحويل بنكي',
    check: 'شيك',
    credit_card: 'بطاقة ائتمان'
  }
  return methodMap[method] || method
}

// تحويل أولوية التوصية إلى نص عربي
export const getPriorityText = (priority) => {
  const priorityMap = {
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    critical: 'حرج'
  }
  return priorityMap[priority] || priority
}

// تحويل دور المستخدم إلى نص عربي
export const getUserRoleText = (role) => {
  const roleMap = {
    admin: 'مدير النظام',
    accountant: 'محاسب',
    project_manager: 'مدير مشروع',
    hr_manager: 'مدير موارد بشرية',
    user: 'مستخدم'
  }
  return roleMap[role] || role
}

// تقصير النص
export const truncateText = (text, maxLength = 50) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// تحويل الحجم بالبايت إلى نص مقروء
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 بايت'
  
  const k = 1024
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// التحقق من صحة البريد الإلكتروني
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// التحقق من صحة الرقم القومي المصري
export const isValidEgyptianNationalId = (id) => {
  if (!id || id.length !== 14) return false
  
  // التحقق من أن جميع الأرقام
  if (!/^\d{14}$/.test(id)) return false
  
  // التحقق من صحة تاريخ الميلاد
  const century = id.charAt(0)
  if (!['2', '3'].includes(century)) return false
  
  return true
}

// التحقق من صحة الرقم الضريبي المصري
export const isValidEgyptianTaxId = (taxId) => {
  if (!taxId) return false
  
  // الرقم الضريبي المصري يتكون من 9 أرقام
  return /^\d{9}$/.test(taxId)
}
