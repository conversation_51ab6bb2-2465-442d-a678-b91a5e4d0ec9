const db = require('../database/connection');

// Audit middleware to track all database changes
const auditMiddleware = (req, res, next) => {
    // Store original res.json to intercept responses
    const originalJson = res.json;
    
    res.json = function(data) {
        // Only audit successful operations that modify data
        if (data && data.success && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
            // Extract audit information
            const auditData = {
                table_name: extractTableName(req.path),
                record_id: extractRecordId(data, req),
                action: getActionFromMethod(req.method),
                old_values: req.body.oldValues ? JSON.stringify(req.body.oldValues) : null,
                new_values: JSON.stringify(req.body),
                user_id: req.user ? req.user.id : null,
                ip_address: req.ip || req.connection.remoteAddress,
                user_agent: req.get('User-Agent')
            };

            // Save audit record asynchronously (don't block response)
            saveAuditRecord(auditData).catch(err => {
                console.error('Audit logging error:', err);
            });
        }
        
        // Call original json method
        originalJson.call(this, data);
    };

    next();
};

// Extract table name from API path
function extractTableName(path) {
    const pathParts = path.split('/');
    if (pathParts.length >= 3) {
        const module = pathParts[2]; // /api/[module]/...
        
        // Map API modules to table names
        const moduleToTable = {
            'accounting': 'chart_of_accounts',
            'projects': 'projects',
            'inventory': 'inventory_items',
            'invoices': 'invoices',
            'payments': 'payments',
            'banking': 'bank_accounts',
            'hr': 'employees',
            'tax': 'tax_settings'
        };
        
        return moduleToTable[module] || module;
    }
    return 'unknown';
}

// Extract record ID from response data or request
function extractRecordId(responseData, req) {
    // Try to get ID from response data
    if (responseData && responseData.data) {
        if (responseData.data.id) return responseData.data.id;
        if (responseData.data.insertId) return responseData.data.insertId;
    }
    
    // Try to get ID from URL parameters
    if (req.params && req.params.id) {
        return parseInt(req.params.id);
    }
    
    return null;
}

// Map HTTP methods to audit actions
function getActionFromMethod(method) {
    const methodMap = {
        'POST': 'INSERT',
        'PUT': 'UPDATE',
        'PATCH': 'UPDATE',
        'DELETE': 'DELETE'
    };
    return methodMap[method] || 'UNKNOWN';
}

// Save audit record to database
async function saveAuditRecord(auditData) {
    try {
        await db.run(`
            INSERT INTO audit_trail (
                table_name, record_id, action, old_values, new_values,
                user_id, ip_address, user_agent, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        `, [
            auditData.table_name,
            auditData.record_id,
            auditData.action,
            auditData.old_values,
            auditData.new_values,
            auditData.user_id,
            auditData.ip_address,
            auditData.user_agent
        ]);
    } catch (error) {
        console.error('Failed to save audit record:', error);
        // Don't throw error to avoid breaking the main operation
    }
}

// System logging function
async function logSystemEvent(level, module, message, details = null, userId = null, ipAddress = null) {
    try {
        await db.run(`
            INSERT INTO system_logs (
                log_level, module, message, details, user_id, ip_address, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        `, [
            level,
            module,
            message,
            details ? JSON.stringify(details) : null,
            userId,
            ipAddress
        ]);
    } catch (error) {
        console.error('Failed to save system log:', error);
    }
}

module.exports = {
    auditMiddleware,
    logSystemEvent
};
