import { forwardRef, useState } from 'react'

const AnimatedCard = forwardRef(({ 
  children, 
  className = '', 
  clickable = false, 
  hover = true,
  padding = 'p-6',
  animation = 'fade-in',
  delay = 0,
  onClick,
  ...props 
}, ref) => {
  const [isVisible, setIsVisible] = useState(false)

  // تأثيرات الحركة
  const animations = {
    'fade-in': 'animate-fadeIn',
    'slide-in': 'animate-slideIn',
    'scale-in': 'animate-scaleIn',
    'bounce-in': 'animate-bounceIn'
  }

  const classes = [
    'enhanced-card',
    padding,
    clickable ? 'clickable' : '',
    hover ? 'hover:shadow-lg hover:-translate-y-2' : '',
    animations[animation] || 'animate-fadeIn',
    'transition-all duration-300',
    className
  ].filter(Boolean).join(' ')

  return (
    <div
      ref={ref}
      className={classes}
      style={{ 
        animationDelay: `${delay}ms`,
        animationFillMode: 'both'
      }}
      onClick={onClick}
      onAnimationEnd={() => setIsVisible(true)}
      {...props}
    >
      {children}
    </div>
  )
})

AnimatedCard.displayName = 'AnimatedCard'

export default AnimatedCard
