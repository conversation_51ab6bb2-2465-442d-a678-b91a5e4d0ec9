const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const SchedulerService = require('./services/scheduler');
require('dotenv').config();

// Import middleware
const authMiddleware = require('./middleware/auth');
const errorHandler = require('./middleware/errorHandler');
const auditMiddleware = require('./middleware/audit');

// Import routes
const authRoutes = require('./routes/auth');
const accountingRoutes = require('./routes/accounting');
const projectRoutes = require('./routes/projects');
const inventoryRoutes = require('./routes/inventory');
const invoiceRoutes = require('./routes/invoices');
const paymentRoutes = require('./routes/payments');
const bankingRoutes = require('./routes/banking');
const hrRoutes = require('./routes/hr');
const taxRoutes = require('./routes/tax');
const reportRoutes = require('./routes/reports');
const aiRoutes = require('./routes/ai');

// Import database connection
const db = require('./database/connection');

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
    origin: process.env.NODE_ENV === 'production' 
        ? ['https://your-domain.com'] 
        : ['http://localhost:5173', 'http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
    app.use(morgan('combined'));
}

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
    });
});

// API routes
app.use('/api/auth', authRoutes);

// Protected routes (require authentication)
app.use('/api/accounting', authMiddleware, auditMiddleware, accountingRoutes);
app.use('/api/projects', authMiddleware, auditMiddleware, projectRoutes);
app.use('/api/inventory', authMiddleware, auditMiddleware, inventoryRoutes);
app.use('/api/invoices', authMiddleware, auditMiddleware, invoiceRoutes);
app.use('/api/payments', authMiddleware, auditMiddleware, paymentRoutes);
app.use('/api/banking', authMiddleware, auditMiddleware, bankingRoutes);
app.use('/api/hr', authMiddleware, auditMiddleware, hrRoutes);
app.use('/api/tax', authMiddleware, auditMiddleware, taxRoutes);
app.use('/api/reports', authMiddleware, reportRoutes);
app.use('/api/ai', authMiddleware, aiRoutes);
app.use('/api/notifications', authMiddleware, require('./routes/notifications'));
app.use('/api/analytics', authMiddleware, require('./routes/analytics'));
app.use('/api/security', require('./routes/security'));

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'API endpoint not found',
        path: req.originalUrl
    });
});

// Global error handler
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    server.close(() => {
        console.log('Process terminated');
        if (db && db.close) {
            db.close();
        }
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    server.close(() => {
        console.log('Process terminated');
        if (db && db.close) {
            db.close();
        }
        process.exit(0);
    });
});

// Start server
const server = app.listen(PORT, () => {
    console.log(`🚀 Construction ERP Server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
    console.log(`💾 Database: ${process.env.DB_TYPE || 'sqlite'}`);

    // Initialize scheduler
    SchedulerService.init();
});

module.exports = app;
