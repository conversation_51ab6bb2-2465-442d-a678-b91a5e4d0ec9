const express = require('express');
const db = require('../database/connection');

const router = express.Router();

// Get AI predictions
router.get('/predictions', async (req, res) => {
    try {
        const predictions = await db.all(`
            SELECT * FROM ai_predictions
            ORDER BY prediction_date DESC
            LIMIT 20
        `);

        res.json({
            success: true,
            data: predictions
        });
    } catch (error) {
        console.error('Get AI predictions error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get AI recommendations
router.get('/recommendations', async (req, res) => {
    try {
        const recommendations = await db.all(`
            SELECT 
                r.*,
                u.first_name || ' ' || u.last_name as assigned_to_name
            FROM ai_recommendations r
            LEFT JOIN users u ON r.assigned_to = u.id
            WHERE r.status != 'dismissed'
            ORDER BY 
                CASE r.priority 
                    WHEN 'critical' THEN 1
                    WHEN 'high' THEN 2
                    WHEN 'medium' THEN 3
                    WHEN 'low' THEN 4
                END,
                r.created_at DESC
        `);

        res.json({
            success: true,
            data: recommendations
        });
    } catch (error) {
        console.error('Get AI recommendations error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
