import { forwardRef } from 'react'

const EnhancedButton = forwardRef(({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  loading = false,
  disabled = false,
  className = '',
  onClick,
  ...props
}, ref) => {
  const variants = {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
    success: 'btn-success',
    warning: 'btn-warning',
    error: 'btn-error',
    ghost: 'bg-transparent text-gray-600 hover:bg-gray-100 border border-gray-300'
  }

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  }

  const classes = [
    'btn-enhanced',
    variants[variant] || 'btn-primary',
    sizes[size] || 'px-4 py-2 text-sm',
    disabled || loading ? 'opacity-50 cursor-not-allowed' : '',
    className
  ].filter(Boolean).join(' ')

  const handleClick = (e) => {
    if (disabled || loading) return
    onClick?.(e)
  }

  return (
    <button
      ref={ref}
      className={classes}
      onClick={handleClick}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <div className="spinner w-4 h-4" />
      )}
      {icon && !loading && (
        <span className="text-lg">{icon}</span>
      )}
      {children}
    </button>
  )
})

EnhancedButton.displayName = 'EnhancedButton'

export default EnhancedButton
