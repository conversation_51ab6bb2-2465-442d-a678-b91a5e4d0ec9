// خدمة الإشعارات
const db = require('../database/connection');

class NotificationService {
    // إنشاء إشعار جديد
    static async createNotification(notificationData) {
        const {
            userId,
            title,
            message,
            type = 'info', // info, warning, error, success
            priority = 'medium', // low, medium, high, urgent
            entityType = null, // invoice, project, payment, etc.
            entityId = null,
            actionUrl = null
        } = notificationData;

        try {
            const result = await db.run(`
                INSERT INTO notifications (
                    user_id, title, message, type, priority,
                    entity_type, entity_id, action_url, is_read, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, datetime('now'))
            `, [userId, title, message, type, priority, entityType, entityId, actionUrl]);

            return {
                id: result.id,
                ...notificationData,
                isRead: false,
                createdAt: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error creating notification:', error);
            throw error;
        }
    }

    // إرسال إشعار لعدة مستخدمين
    static async createBulkNotification(userIds, notificationData) {
        const notifications = [];
        
        for (const userId of userIds) {
            const notification = await this.createNotification({
                ...notificationData,
                userId
            });
            notifications.push(notification);
        }
        
        return notifications;
    }

    // إرسال إشعار لجميع المستخدمين بدور معين
    static async createRoleNotification(role, notificationData) {
        try {
            const users = await db.all(
                'SELECT id FROM users WHERE role = ? AND is_active = 1',
                [role]
            );
            
            const userIds = users.map(user => user.id);
            return await this.createBulkNotification(userIds, notificationData);
        } catch (error) {
            console.error('Error creating role notification:', error);
            throw error;
        }
    }

    // الحصول على إشعارات المستخدم
    static async getUserNotifications(userId, options = {}) {
        const {
            isRead = null,
            type = null,
            limit = 50,
            offset = 0
        } = options;

        let whereClause = 'user_id = ?';
        let params = [userId];

        if (isRead !== null) {
            whereClause += ' AND is_read = ?';
            params.push(isRead ? 1 : 0);
        }

        if (type) {
            whereClause += ' AND type = ?';
            params.push(type);
        }

        try {
            const notifications = await db.all(`
                SELECT * FROM notifications
                WHERE ${whereClause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            `, [...params, limit, offset]);

            return notifications;
        } catch (error) {
            console.error('Error getting user notifications:', error);
            throw error;
        }
    }

    // عدد الإشعارات غير المقروءة
    static async getUnreadCount(userId) {
        try {
            const result = await db.get(
                'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0',
                [userId]
            );
            return result.count;
        } catch (error) {
            console.error('Error getting unread count:', error);
            throw error;
        }
    }

    // تحديد إشعار كمقروء
    static async markAsRead(notificationId, userId) {
        try {
            await db.run(
                'UPDATE notifications SET is_read = 1, read_at = datetime("now") WHERE id = ? AND user_id = ?',
                [notificationId, userId]
            );
            return true;
        } catch (error) {
            console.error('Error marking notification as read:', error);
            throw error;
        }
    }

    // تحديد جميع الإشعارات كمقروءة
    static async markAllAsRead(userId) {
        try {
            await db.run(
                'UPDATE notifications SET is_read = 1, read_at = datetime("now") WHERE user_id = ? AND is_read = 0',
                [userId]
            );
            return true;
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
            throw error;
        }
    }

    // حذف إشعار
    static async deleteNotification(notificationId, userId) {
        try {
            await db.run(
                'DELETE FROM notifications WHERE id = ? AND user_id = ?',
                [notificationId, userId]
            );
            return true;
        } catch (error) {
            console.error('Error deleting notification:', error);
            throw error;
        }
    }

    // فحص الفواتير المستحقة وإرسال إشعارات
    static async checkOverdueInvoices() {
        try {
            const overdueInvoices = await db.all(`
                SELECT i.*, c.customer_name, s.supplier_name
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                WHERE i.due_date < date('now') 
                AND i.status NOT IN ('paid', 'cancelled')
                AND i.id NOT IN (
                    SELECT entity_id FROM notifications 
                    WHERE entity_type = 'overdue_invoice' 
                    AND created_at > date('now', '-1 day')
                )
            `);

            for (const invoice of overdueInvoices) {
                const partyName = invoice.customer_name || invoice.supplier_name;
                const invoiceType = invoice.invoice_type === 'sales' ? 'مبيعات' : 'مشتريات';
                
                await this.createRoleNotification(['admin', 'accountant'], {
                    title: `فاتورة ${invoiceType} متأخرة`,
                    message: `فاتورة ${invoiceType} رقم ${invoice.invoice_number} من ${partyName} متأخرة السداد`,
                    type: 'warning',
                    priority: 'high',
                    entityType: 'overdue_invoice',
                    entityId: invoice.id,
                    actionUrl: `/invoices/${invoice.id}`
                });
            }

            return overdueInvoices.length;
        } catch (error) {
            console.error('Error checking overdue invoices:', error);
            throw error;
        }
    }

    // فحص المخزون المنخفض وإرسال إشعارات
    static async checkLowStock() {
        try {
            const lowStockItems = await db.all(`
                SELECT * FROM inventory_items
                WHERE current_stock <= minimum_stock
                AND is_active = 1
                AND id NOT IN (
                    SELECT entity_id FROM notifications 
                    WHERE entity_type = 'low_stock' 
                    AND created_at > date('now', '-1 day')
                )
            `);

            for (const item of lowStockItems) {
                await this.createRoleNotification(['admin', 'project_manager'], {
                    title: 'مخزون منخفض',
                    message: `الصنف ${item.item_name} (${item.item_code}) وصل للحد الأدنى. الرصيد الحالي: ${item.current_stock}`,
                    type: 'warning',
                    priority: 'medium',
                    entityType: 'low_stock',
                    entityId: item.id,
                    actionUrl: `/inventory/${item.id}`
                });
            }

            return lowStockItems.length;
        } catch (error) {
            console.error('Error checking low stock:', error);
            throw error;
        }
    }

    // فحص المشاريع المتأخرة
    static async checkDelayedProjects() {
        try {
            const delayedProjects = await db.all(`
                SELECT p.*, u.first_name || ' ' || u.last_name as manager_name
                FROM projects p
                LEFT JOIN users u ON p.project_manager_id = u.id
                WHERE p.end_date < date('now')
                AND p.status IN ('active', 'planning')
                AND p.id NOT IN (
                    SELECT entity_id FROM notifications 
                    WHERE entity_type = 'delayed_project' 
                    AND created_at > date('now', '-1 day')
                )
            `);

            for (const project of delayedProjects) {
                await this.createRoleNotification(['admin', 'project_manager'], {
                    title: 'مشروع متأخر',
                    message: `المشروع ${project.project_name} متأخر عن الموعد المحدد`,
                    type: 'error',
                    priority: 'high',
                    entityType: 'delayed_project',
                    entityId: project.id,
                    actionUrl: `/projects/${project.id}`
                });

                // إشعار خاص لمدير المشروع
                if (project.project_manager_id) {
                    await this.createNotification({
                        userId: project.project_manager_id,
                        title: 'مشروعك متأخر',
                        message: `المشروع ${project.project_name} الذي تديره متأخر عن الموعد المحدد`,
                        type: 'error',
                        priority: 'urgent',
                        entityType: 'delayed_project',
                        entityId: project.id,
                        actionUrl: `/projects/${project.id}`
                    });
                }
            }

            return delayedProjects.length;
        } catch (error) {
            console.error('Error checking delayed projects:', error);
            throw error;
        }
    }

    // فحص تجاوز ميزانية المشاريع
    static async checkBudgetOverruns() {
        try {
            const overrunProjects = await db.all(`
                SELECT 
                    p.*,
                    COALESCE(SUM(pt.amount), 0) as actual_cost,
                    u.first_name || ' ' || u.last_name as manager_name
                FROM projects p
                LEFT JOIN project_transactions pt ON p.id = pt.project_id
                LEFT JOIN users u ON p.project_manager_id = u.id
                WHERE p.estimated_budget > 0
                AND p.status IN ('active', 'planning')
                GROUP BY p.id
                HAVING actual_cost > p.estimated_budget * 0.9
                AND p.id NOT IN (
                    SELECT entity_id FROM notifications 
                    WHERE entity_type = 'budget_overrun' 
                    AND created_at > date('now', '-1 day')
                )
            `);

            for (const project of overrunProjects) {
                const overrunPercentage = ((project.actual_cost - project.estimated_budget) / project.estimated_budget * 100).toFixed(1);
                
                await this.createRoleNotification(['admin', 'project_manager'], {
                    title: 'تجاوز ميزانية مشروع',
                    message: `المشروع ${project.project_name} تجاوز الميزانية بنسبة ${overrunPercentage}%`,
                    type: 'error',
                    priority: 'high',
                    entityType: 'budget_overrun',
                    entityId: project.id,
                    actionUrl: `/projects/${project.id}`
                });
            }

            return overrunProjects.length;
        } catch (error) {
            console.error('Error checking budget overruns:', error);
            throw error;
        }
    }

    // تشغيل جميع الفحوصات التلقائية
    static async runAutomaticChecks() {
        try {
            console.log('Running automatic notification checks...');
            
            const results = {
                overdueInvoices: await this.checkOverdueInvoices(),
                lowStock: await this.checkLowStock(),
                delayedProjects: await this.checkDelayedProjects(),
                budgetOverruns: await this.checkBudgetOverruns()
            };

            console.log('Automatic checks completed:', results);
            return results;
        } catch (error) {
            console.error('Error running automatic checks:', error);
            throw error;
        }
    }
}

module.exports = NotificationService;
