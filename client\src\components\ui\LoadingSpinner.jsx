import { forwardRef } from 'react'

const LoadingSpinner = forwardRef(({ 
  size = 'md',
  color = 'primary',
  className = '',
  ...props 
}, ref) => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const colors = {
    primary: 'border-blue-600',
    secondary: 'border-gray-600',
    success: 'border-green-600',
    warning: 'border-yellow-600',
    error: 'border-red-600'
  }

  const baseClasses = `
    spinner
    ${sizes[size]}
    ${colors[color]}
    ${className}
  `

  return (
    <div
      ref={ref}
      className={baseClasses.trim()}
      {...props}
    />
  )
})

LoadingSpinner.displayName = 'LoadingSpinner'

export default LoadingSpinner
