import { useState } from 'react'
import { Enhanced<PERSON>ard, EnhancedButton, StatusBadge, ProgressBar } from '../../components/ui'

function ProjectManagement() {
  const [activeTab, setActiveTab] = useState('projects')
  const [selectedProject, setSelectedProject] = useState(null)
  const [showGanttChart, setShowGanttChart] = useState(false)
  const [weatherAlert, setWeatherAlert] = useState({
    show: true,
    message: 'تحذير: أمطار متوقعة غداً - قد تؤثر على أعمال الخرسانة',
    severity: 'warning'
  })

  // بيانات المشاريع
  const projects = [
    {
      id: 'PRJ-001',
      name: 'مشروع الفيلا السكنية',
      client: 'أحمد علي محمد',
      status: 'قيد التنفيذ',
      startDate: '2024-01-01',
      endDate: '2024-06-30',
      budget: 500000,
      spent: 375000,
      progress: 75,
      location: 'التجمع الخامس، القاهرة الجديدة',
      description: 'إنشاء فيلا سكنية مكونة من دورين بمساحة 300 متر مربع',
      costCenters: [
        { code: 'CC-001-01', name: 'أعمال الحفر والأساسات', budget: 100000, spent: 95000 },
        { code: 'CC-001-02', name: 'أعمال الخرسانة المسلحة', budget: 150000, spent: 145000 },
        { code: 'CC-001-03', name: 'أعمال المباني', budget: 120000, spent: 90000 },
        { code: 'CC-001-04', name: 'أعمال التشطيبات', budget: 100000, spent: 35000 },
        { code: 'CC-001-05', name: 'أعمال الكهرباء والسباكة', budget: 30000, spent: 10000 }
      ]
    },
    {
      id: 'PRJ-002',
      name: 'مشروع المجمع التجاري',
      client: 'شركة الاستثمار العقاري',
      status: 'قيد التنفيذ',
      startDate: '2024-02-01',
      endDate: '2024-12-31',
      budget: 1200000,
      spent: 540000,
      progress: 45,
      location: 'مدينة نصر، القاهرة',
      description: 'إنشاء مجمع تجاري مكون من 3 طوابق بمساحة 1500 متر مربع',
      costCenters: [
        { code: 'CC-002-01', name: 'أعمال الحفر والأساسات', budget: 200000, spent: 190000 },
        { code: 'CC-002-02', name: 'أعمال الخرسانة المسلحة', budget: 400000, spent: 250000 },
        { code: 'CC-002-03', name: 'أعمال المباني', budget: 300000, spent: 100000 },
        { code: 'CC-002-04', name: 'أعمال التشطيبات', budget: 200000, spent: 0 },
        { code: 'CC-002-05', name: 'أعمال الكهرباء والسباكة', budget: 100000, spent: 0 }
      ]
    },
    {
      id: 'PRJ-003',
      name: 'مشروع المدرسة الابتدائية',
      client: 'وزارة التربية والتعليم',
      status: 'قيد التنفيذ',
      startDate: '2024-03-01',
      endDate: '2024-09-30',
      budget: 800000,
      spent: 240000,
      progress: 30,
      location: 'الجيزة',
      description: 'إنشاء مدرسة ابتدائية مكونة من دورين بـ 20 فصل دراسي',
      costCenters: [
        { code: 'CC-003-01', name: 'أعمال الحفر والأساسات', budget: 120000, spent: 115000 },
        { code: 'CC-003-02', name: 'أعمال الخرسانة المسلحة', budget: 250000, spent: 125000 },
        { code: 'CC-003-03', name: 'أعمال المباني', budget: 200000, spent: 0 },
        { code: 'CC-003-04', name: 'أعمال التشطيبات', budget: 150000, spent: 0 },
        { code: 'CC-003-05', name: 'أعمال الكهرباء والسباكة', budget: 80000, spent: 0 }
      ]
    }
  ]

  // مستخلصات المشاريع
  const projectExtracts = [
    {
      id: 'EXT-001',
      projectId: 'PRJ-001',
      extractNumber: 1,
      date: '2024-01-31',
      description: 'المستخلص الأول - أعمال الحفر والأساسات',
      amount: 95000,
      status: 'مدفوع',
      workCompleted: 'أعمال الحفر والأساسات مكتملة 100%'
    },
    {
      id: 'EXT-002',
      projectId: 'PRJ-001',
      extractNumber: 2,
      date: '2024-02-28',
      description: 'المستخلص الثاني - أعمال الخرسانة المسلحة',
      amount: 145000,
      status: 'مدفوع',
      workCompleted: 'أعمال الخرسانة المسلحة مكتملة 100%'
    },
    {
      id: 'EXT-003',
      projectId: 'PRJ-001',
      extractNumber: 3,
      date: '2024-03-31',
      description: 'المستخلص الثالث - أعمال المباني جزئي',
      amount: 90000,
      status: 'قيد المراجعة',
      workCompleted: 'أعمال المباني مكتملة 75%'
    }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case 'قيد التنفيذ': return 'bg-blue-100 text-blue-800'
      case 'مكتمل': return 'bg-green-100 text-green-800'
      case 'متوقف': return 'bg-red-100 text-red-800'
      case 'مدفوع': return 'bg-green-100 text-green-800'
      case 'قيد المراجعة': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getProgressColor = (progress) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const tabs = [
    { id: 'projects', name: 'المشاريع', icon: '🏗️' },
    { id: 'cost-centers', name: 'مراكز التكلفة', icon: '💰' },
    { id: 'extracts', name: 'المستخلصات', icon: '📋' },
    { id: 'budget-analysis', name: 'تحليل الميزانية', icon: '📊' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                🏗️ إدارة المشاريع والمقاولات
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => alert('🔄 جاري إضافة مشروع جديد...')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                ➕ مشروع جديد
              </button>
              <button
                onClick={() => alert('📊 جاري إنشاء تقرير المشاريع...')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📋 تقرير شامل
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              
              {/* المشاريع */}
              {activeTab === 'projects' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">🏗️ قائمة المشاريع</h2>
                  
                  {/* إحصائيات سريعة */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-blue-800 text-sm font-medium">إجمالي المشاريع</div>
                      <div className="text-blue-900 text-2xl font-bold">{projects.length}</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-green-800 text-sm font-medium">إجمالي الميزانيات</div>
                      <div className="text-green-900 text-lg font-bold">
                        {projects.reduce((sum, p) => sum + p.budget, 0).toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-yellow-800 text-sm font-medium">إجمالي المصروف</div>
                      <div className="text-yellow-900 text-lg font-bold">
                        {projects.reduce((sum, p) => sum + p.spent, 0).toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-purple-800 text-sm font-medium">متوسط التقدم</div>
                      <div className="text-purple-900 text-2xl font-bold">
                        {Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length)}%
                      </div>
                    </div>
                  </div>

                  {/* جدول المشاريع */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            رمز المشروع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            اسم المشروع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            العميل
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الحالة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            التقدم
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الميزانية
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المصروف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            إجراءات
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {projects.map((project) => (
                          <tr key={project.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                              {project.id}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {project.name}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {project.client}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(project.status)}`}>
                                {project.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                  <div 
                                    className={`h-2 rounded-full ${getProgressColor(project.progress)}`}
                                    style={{ width: `${project.progress}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium">{project.progress}%</span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {project.budget.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {project.spent.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <button 
                                onClick={() => setSelectedProject(project)}
                                className="text-blue-600 hover:text-blue-900 ml-4"
                              >
                                عرض
                              </button>
                              <button 
                                onClick={() => alert(`تعديل المشروع: ${project.name}`)}
                                className="text-green-600 hover:text-green-900"
                              >
                                تعديل
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* مراكز التكلفة */}
              {activeTab === 'cost-centers' && selectedProject && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">
                    💰 مراكز التكلفة - {selectedProject.name}
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {selectedProject.costCenters.map((center) => (
                      <div key={center.code} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-medium text-gray-900">{center.code}</h3>
                          <span className="text-sm text-gray-500">
                            {Math.round((center.spent / center.budget) * 100)}%
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{center.name}</p>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">الميزانية:</span>
                            <span className="font-medium">{center.budget.toLocaleString('ar-EG')} ج.م</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">المصروف:</span>
                            <span className="font-medium text-red-600">{center.spent.toLocaleString('ar-EG')} ج.م</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">المتبقي:</span>
                            <span className="font-medium text-green-600">
                              {(center.budget - center.spent).toLocaleString('ar-EG')} ج.م
                            </span>
                          </div>
                        </div>
                        
                        <div className="mt-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                (center.spent / center.budget) > 0.9 ? 'bg-red-500' :
                                (center.spent / center.budget) > 0.7 ? 'bg-yellow-500' : 'bg-green-500'
                              }`}
                              style={{ width: `${Math.min((center.spent / center.budget) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* المستخلصات */}
              {activeTab === 'extracts' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📋 مستخلصات المشاريع</h2>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            رقم المستخلص
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المشروع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            التاريخ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الوصف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المبلغ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الحالة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            إجراءات
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {projectExtracts.map((extract) => {
                          const project = projects.find(p => p.id === extract.projectId)
                          return (
                            <tr key={extract.id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                                {extract.extractNumber}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900">
                                {project?.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {extract.date}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900">
                                {extract.description}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {extract.amount.toLocaleString('ar-EG')} ج.م
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(extract.status)}`}>
                                  {extract.status}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button 
                                  onClick={() => alert(`عرض تفاصيل المستخلص: ${extract.extractNumber}`)}
                                  className="text-blue-600 hover:text-blue-900 ml-4"
                                >
                                  عرض
                                </button>
                                <button 
                                  onClick={() => alert(`طباعة المستخلص: ${extract.extractNumber}`)}
                                  className="text-green-600 hover:text-green-900"
                                >
                                  طباعة
                                </button>
                              </td>
                            </tr>
                          )
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* تحليل الميزانية */}
              {activeTab === 'budget-analysis' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📊 تحليل انحراف الميزانية</h2>
                  
                  <div className="space-y-6">
                    {projects.map((project) => {
                      const variance = project.budget - project.spent
                      const variancePercent = ((variance / project.budget) * 100).toFixed(1)
                      
                      return (
                        <div key={project.id} className="border border-gray-200 rounded-lg p-6">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">{project.name}</h3>
                            <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                              variance >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {variance >= 0 ? 'في الميزانية' : 'تجاوز الميزانية'}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="bg-blue-50 p-4 rounded-lg">
                              <div className="text-blue-800 text-sm font-medium">الميزانية المقررة</div>
                              <div className="text-blue-900 text-lg font-bold">
                                {project.budget.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                            <div className="bg-red-50 p-4 rounded-lg">
                              <div className="text-red-800 text-sm font-medium">المصروف الفعلي</div>
                              <div className="text-red-900 text-lg font-bold">
                                {project.spent.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                            <div className={`p-4 rounded-lg ${variance >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
                              <div className={`text-sm font-medium ${variance >= 0 ? 'text-green-800' : 'text-red-800'}`}>
                                الانحراف
                              </div>
                              <div className={`text-lg font-bold ${variance >= 0 ? 'text-green-900' : 'text-red-900'}`}>
                                {variance.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg">
                              <div className="text-gray-800 text-sm font-medium">نسبة الانحراف</div>
                              <div className={`text-lg font-bold ${variance >= 0 ? 'text-green-900' : 'text-red-900'}`}>
                                {variancePercent}%
                              </div>
                            </div>
                          </div>
                          
                          <div className="mt-4">
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>التقدم: {project.progress}%</span>
                              <span>المصروف: {((project.spent / project.budget) * 100).toFixed(1)}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3">
                              <div className="relative h-3 rounded-full">
                                <div 
                                  className="absolute top-0 right-0 h-3 bg-blue-500 rounded-full"
                                  style={{ width: `${project.progress}%` }}
                                ></div>
                                <div 
                                  className="absolute top-0 right-0 h-3 bg-red-500 rounded-full opacity-70"
                                  style={{ width: `${Math.min((project.spent / project.budget) * 100, 100)}%` }}
                                ></div>
                              </div>
                            </div>
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                              <span>🔵 التقدم الفعلي</span>
                              <span>🔴 المصروف النسبي</span>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}

              {/* رسالة اختيار مشروع لمراكز التكلفة */}
              {activeTab === 'cost-centers' && !selectedProject && (
                <div className="text-center text-gray-500 py-12">
                  <div className="text-4xl mb-4">💰</div>
                  <h3 className="text-lg font-medium mb-2">اختر مشروعاً لعرض مراكز التكلفة</h3>
                  <p className="text-sm mb-4">انتقل إلى تبويب المشاريع واختر مشروعاً لعرض مراكز التكلفة الخاصة به</p>
                  <button
                    onClick={() => setActiveTab('projects')}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    عرض المشاريع
                  </button>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProjectManagement
