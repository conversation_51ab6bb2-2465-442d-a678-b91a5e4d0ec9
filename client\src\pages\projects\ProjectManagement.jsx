import { useState } from 'react'
import { Enhanced<PERSON>ard, EnhancedButton, StatusBadge, ProgressBar } from '../../components/ui'

function ProjectManagement() {
  const [activeTab, setActiveTab] = useState('projects')
  const [selectedProject, setSelectedProject] = useState(null)
  const [showGanttChart, setShowGanttChart] = useState(false)
  const [showNewProjectModal, setShowNewProjectModal] = useState(false)
  const [showCostCenterModal, setShowCostCenterModal] = useState(false)
  const [showNewCostCenterModal, setShowNewCostCenterModal] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [weatherAlert, setWeatherAlert] = useState({
    show: true,
    message: 'تحذير: أمطار متوقعة غداً - قد تؤثر على أعمال الخرسانة',
    severity: 'warning'
  })

  // حالة نموذج المشروع الجديد
  const [newProject, setNewProject] = useState({
    projectCode: '',
    projectName: '',
    clientName: '',
    clientContact: '',
    projectType: 'civil',
    startDate: '',
    endDate: '',
    estimatedBudget: '',
    description: '',
    location: ''
  })

  // حالة التقارير
  const [showReportModal, setShowReportModal] = useState(false)
  const [selectedReportType, setSelectedReportType] = useState('summary')
  const [reportDateRange, setReportDateRange] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  })
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)

  // بيانات المشاريع - تحويل إلى state
  const [projects, setProjects] = useState([
    {
      id: 'PRJ-001',
      name: 'مشروع الفيلا السكنية',
      client: 'أحمد علي محمد',
      status: 'قيد التنفيذ',
      startDate: '2024-01-01',
      endDate: '2024-06-30',
      budget: 500000,
      spent: 375000,
      progress: 75,
      location: 'التجمع الخامس، القاهرة الجديدة',
      description: 'إنشاء فيلا سكنية مكونة من دورين بمساحة 300 متر مربع',
      costCenters: [
        { code: 'CC-001-01', name: 'أعمال الحفر والأساسات', budget: 100000, spent: 95000 },
        { code: 'CC-001-02', name: 'أعمال الخرسانة المسلحة', budget: 150000, spent: 145000 },
        { code: 'CC-001-03', name: 'أعمال المباني', budget: 120000, spent: 90000 },
        { code: 'CC-001-04', name: 'أعمال التشطيبات', budget: 100000, spent: 35000 },
        { code: 'CC-001-05', name: 'أعمال الكهرباء والسباكة', budget: 30000, spent: 10000 }
      ]
    },
    {
      id: 'PRJ-002',
      name: 'مشروع المجمع التجاري',
      client: 'شركة الاستثمار العقاري',
      status: 'قيد التنفيذ',
      startDate: '2024-02-01',
      endDate: '2024-12-31',
      budget: 1200000,
      spent: 540000,
      progress: 45,
      location: 'مدينة نصر، القاهرة',
      description: 'إنشاء مجمع تجاري مكون من 3 طوابق بمساحة 1500 متر مربع',
      costCenters: [
        { code: 'CC-002-01', name: 'أعمال الحفر والأساسات', budget: 200000, spent: 190000 },
        { code: 'CC-002-02', name: 'أعمال الخرسانة المسلحة', budget: 400000, spent: 250000 },
        { code: 'CC-002-03', name: 'أعمال المباني', budget: 300000, spent: 100000 },
        { code: 'CC-002-04', name: 'أعمال التشطيبات', budget: 200000, spent: 0 },
        { code: 'CC-002-05', name: 'أعمال الكهرباء والسباكة', budget: 100000, spent: 0 }
      ]
    },
    {
      id: 'PRJ-003',
      name: 'مشروع المدرسة الابتدائية',
      client: 'وزارة التربية والتعليم',
      status: 'قيد التنفيذ',
      startDate: '2024-03-01',
      endDate: '2024-09-30',
      budget: 800000,
      spent: 240000,
      progress: 30,
      location: 'الجيزة',
      description: 'إنشاء مدرسة ابتدائية مكونة من دورين بـ 20 فصل دراسي',
      costCenters: [
        { code: 'CC-003-01', name: 'أعمال الحفر والأساسات', budget: 120000, spent: 115000 },
        { code: 'CC-003-02', name: 'أعمال الخرسانة المسلحة', budget: 250000, spent: 125000 },
        { code: 'CC-003-03', name: 'أعمال المباني', budget: 200000, spent: 0 },
        { code: 'CC-003-04', name: 'أعمال التشطيبات', budget: 150000, spent: 0 },
        { code: 'CC-003-05', name: 'أعمال الكهرباء والسباكة', budget: 80000, spent: 0 }
      ]
    }
  ])

  // مراكز التكلفة - state منفصل لسهولة الإدارة
  const [costCenters, setCostCenters] = useState([
    {
      id: 'CC-001',
      projectId: 'PRJ-001',
      code: 'CC-001-01',
      name: 'أعمال الحفر والأساسات',
      budget: 100000,
      spent: 95000,
      remaining: 5000,
      progress: 95,
      status: 'مكتمل',
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      responsible: 'م. محمد أحمد',
      description: 'أعمال الحفر والأساسات للمشروع',
      expenses: [
        { id: 'EXP-001', description: 'حفر الأساسات', amount: 25000, date: '2024-01-05' },
        { id: 'EXP-002', description: 'خرسانة الأساسات', amount: 45000, date: '2024-01-15' },
        { id: 'EXP-003', description: 'حديد التسليح', amount: 25000, date: '2024-01-25' }
      ]
    },
    {
      id: 'CC-002',
      projectId: 'PRJ-001',
      code: 'CC-001-02',
      name: 'أعمال الخرسانة المسلحة',
      budget: 150000,
      spent: 145000,
      remaining: 5000,
      progress: 97,
      status: 'مكتمل',
      startDate: '2024-02-01',
      endDate: '2024-02-28',
      responsible: 'م. سارة علي',
      description: 'أعمال الخرسانة المسلحة للأعمدة والأسقف',
      expenses: [
        { id: 'EXP-004', description: 'خرسانة الأعمدة', amount: 60000, date: '2024-02-10' },
        { id: 'EXP-005', description: 'خرسانة الأسقف', amount: 85000, date: '2024-02-25' }
      ]
    },
    {
      id: 'CC-003',
      projectId: 'PRJ-001',
      code: 'CC-001-03',
      name: 'أعمال المباني',
      budget: 120000,
      spent: 90000,
      remaining: 30000,
      progress: 75,
      status: 'قيد التنفيذ',
      startDate: '2024-03-01',
      endDate: '2024-04-15',
      responsible: 'م. أحمد محمود',
      description: 'أعمال المباني والطوب',
      expenses: [
        { id: 'EXP-006', description: 'طوب أحمر', amount: 40000, date: '2024-03-05' },
        { id: 'EXP-007', description: 'أسمنت ومونة', amount: 30000, date: '2024-03-15' },
        { id: 'EXP-008', description: 'عمالة البناء', amount: 20000, date: '2024-03-25' }
      ]
    },
    {
      id: 'CC-004',
      projectId: 'PRJ-001',
      code: 'CC-001-04',
      name: 'أعمال التشطيبات',
      budget: 100000,
      spent: 15000,
      remaining: 85000,
      progress: 15,
      status: 'قيد التنفيذ',
      startDate: '2024-04-01',
      endDate: '2024-05-31',
      responsible: 'م. فاطمة حسن',
      description: 'أعمال التشطيبات الداخلية والخارجية',
      expenses: [
        { id: 'EXP-009', description: 'مواد التشطيب', amount: 15000, date: '2024-04-05' }
      ]
    },
    {
      id: 'CC-005',
      projectId: 'PRJ-001',
      code: 'CC-001-05',
      name: 'أعمال الكهرباء والسباكة',
      budget: 80000,
      spent: 0,
      remaining: 80000,
      progress: 0,
      status: 'لم يبدأ',
      startDate: '2024-05-01',
      endDate: '2024-06-15',
      responsible: 'م. خالد عبدالله',
      description: 'أعمال الكهرباء والسباكة',
      expenses: []
    }
  ])

  // state لمركز التكلفة الجديد
  const [newCostCenter, setNewCostCenter] = useState({
    code: '',
    name: '',
    budget: '',
    responsible: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    description: ''
  })

  // مستخلصات المشاريع
  const projectExtracts = [
    {
      id: 'EXT-001',
      projectId: 'PRJ-001',
      extractNumber: 1,
      date: '2024-01-31',
      description: 'المستخلص الأول - أعمال الحفر والأساسات',
      amount: 95000,
      status: 'مدفوع',
      workCompleted: 'أعمال الحفر والأساسات مكتملة 100%'
    },
    {
      id: 'EXT-002',
      projectId: 'PRJ-001',
      extractNumber: 2,
      date: '2024-02-28',
      description: 'المستخلص الثاني - أعمال الخرسانة المسلحة',
      amount: 145000,
      status: 'مدفوع',
      workCompleted: 'أعمال الخرسانة المسلحة مكتملة 100%'
    },
    {
      id: 'EXT-003',
      projectId: 'PRJ-001',
      extractNumber: 3,
      date: '2024-03-31',
      description: 'المستخلص الثالث - أعمال المباني جزئي',
      amount: 90000,
      status: 'قيد المراجعة',
      workCompleted: 'أعمال المباني مكتملة 75%'
    }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case 'قيد التنفيذ': return 'bg-blue-100 text-blue-800'
      case 'مكتمل': return 'bg-green-100 text-green-800'
      case 'متوقف': return 'bg-red-100 text-red-800'
      case 'مدفوع': return 'bg-green-100 text-green-800'
      case 'قيد المراجعة': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getProgressColor = (progress) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  // وظائف إدارة المشروع الجديد
  const handleNewProject = () => {
    setShowNewProjectModal(true)
  }

  const handleSaveProject = async () => {
    // التحقق من صحة البيانات
    if (!newProject.projectCode || !newProject.projectName || !newProject.clientName) {
      alert('⚠️ يرجى ملء جميع الحقول المطلوبة')
      return
    }

    setIsLoading(true)

    try {
      // محاكاة حفظ المشروع
      await new Promise(resolve => setTimeout(resolve, 2000))

      alert(`✅ تم إنشاء المشروع بنجاح!\n\nكود المشروع: ${newProject.projectCode}\nاسم المشروع: ${newProject.projectName}\nالعميل: ${newProject.clientName}`)

      // إعادة تعيين النموذج
      setNewProject({
        projectCode: '',
        projectName: '',
        clientName: '',
        clientContact: '',
        projectType: 'civil',
        startDate: '',
        endDate: '',
        estimatedBudget: '',
        description: '',
        location: ''
      })

      setShowNewProjectModal(false)
    } catch (error) {
      alert('❌ حدث خطأ أثناء إنشاء المشروع')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelProject = () => {
    setShowNewProjectModal(false)
    setNewProject({
      projectCode: '',
      projectName: '',
      clientName: '',
      clientContact: '',
      projectType: 'civil',
      startDate: '',
      endDate: '',
      estimatedBudget: '',
      description: '',
      location: ''
    })
  }

  // وظائف إدارة مراكز التكلفة
  const handleViewCostCenters = (project) => {
    setSelectedProject(project)
    setShowCostCenterModal(true)
  }

  const handleNewCostCenter = () => {
    if (!selectedProject) {
      alert('⚠️ يرجى اختيار مشروع أولاً')
      return
    }
    setShowNewCostCenterModal(true)
  }

  const handleSaveCostCenter = async () => {
    if (!newCostCenter.code || !newCostCenter.name || !newCostCenter.budget) {
      alert('⚠️ يرجى ملء جميع الحقول المطلوبة (الكود، الاسم، الميزانية)')
      return
    }

    if (parseFloat(newCostCenter.budget) <= 0) {
      alert('⚠️ يجب أن تكون الميزانية أكبر من صفر')
      return
    }

    setIsLoading(true)

    try {
      await new Promise(resolve => setTimeout(resolve, 2000))

      const newCostCenterData = {
        id: `CC-${String(costCenters.length + 1).padStart(3, '0')}`,
        projectId: selectedProject.id,
        code: newCostCenter.code,
        name: newCostCenter.name,
        budget: parseFloat(newCostCenter.budget),
        spent: 0,
        remaining: parseFloat(newCostCenter.budget),
        progress: 0,
        status: 'لم يبدأ',
        startDate: newCostCenter.startDate,
        endDate: newCostCenter.endDate,
        responsible: newCostCenter.responsible || 'غير محدد',
        description: newCostCenter.description,
        expenses: []
      }

      setCostCenters(prevCostCenters => [...prevCostCenters, newCostCenterData])

      alert(`✅ تم إنشاء مركز التكلفة بنجاح!\n\n` +
            `الكود: ${newCostCenter.code}\n` +
            `الاسم: ${newCostCenter.name}\n` +
            `الميزانية: ${parseFloat(newCostCenter.budget).toLocaleString('ar-EG')} ج.م\n` +
            `المسؤول: ${newCostCenter.responsible || 'غير محدد'}`)

      setNewCostCenter({
        code: '',
        name: '',
        budget: '',
        responsible: '',
        startDate: new Date().toISOString().split('T')[0],
        endDate: '',
        description: ''
      })

      setShowNewCostCenterModal(false)
    } catch (error) {
      alert('❌ حدث خطأ أثناء إنشاء مركز التكلفة')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelCostCenter = () => {
    setShowNewCostCenterModal(false)
    setNewCostCenter({
      code: '',
      name: '',
      budget: '',
      responsible: '',
      startDate: new Date().toISOString().split('T')[0],
      endDate: '',
      description: ''
    })
  }

  const handleAddExpenseToCostCenter = (costCenterId) => {
    const expense = {
      description: prompt('وصف المصروف:'),
      amount: prompt('المبلغ:'),
      date: new Date().toISOString().split('T')[0]
    }

    if (expense.description && expense.amount) {
      const amount = parseFloat(expense.amount)
      if (amount > 0) {
        setCostCenters(prevCostCenters =>
          prevCostCenters.map(cc =>
            cc.id === costCenterId
              ? {
                  ...cc,
                  expenses: [...cc.expenses, {
                    id: `EXP-${Date.now()}`,
                    description: expense.description,
                    amount: amount,
                    date: expense.date
                  }],
                  spent: cc.spent + amount,
                  remaining: cc.budget - (cc.spent + amount),
                  progress: Math.round(((cc.spent + amount) / cc.budget) * 100)
                }
              : cc
          )
        )

        alert(`✅ تم إضافة المصروف بنجاح!\n\nالوصف: ${expense.description}\nالمبلغ: ${amount.toLocaleString('ar-EG')} ج.م`)
      }
    }
  }

  const handleUpdateCostCenterStatus = (costCenterId, newStatus) => {
    setCostCenters(prevCostCenters =>
      prevCostCenters.map(cc =>
        cc.id === costCenterId
          ? { ...cc, status: newStatus }
          : cc
      )
    )

    const costCenter = costCenters.find(cc => cc.id === costCenterId)
    alert(`✅ تم تحديث حالة مركز التكلفة "${costCenter.name}" إلى: ${newStatus}`)
  }

  // وظائف التقارير
  const handleGenerateReport = () => {
    setShowReportModal(true)
  }

  const handleCreateReport = async () => {
    if (!reportDateRange.startDate || !reportDateRange.endDate) {
      alert('⚠️ يرجى تحديد نطاق التاريخ للتقرير')
      return
    }

    setIsGeneratingReport(true)

    try {
      // محاكاة إنشاء التقرير
      await new Promise(resolve => setTimeout(resolve, 3000))

      const reportTypes = {
        summary: 'تقرير ملخص المشاريع',
        detailed: 'تقرير تفصيلي للمشاريع',
        financial: 'التقرير المالي للمشاريع',
        progress: 'تقرير تقدم المشاريع',
        costs: 'تقرير تكاليف المشاريع'
      }

      alert(`✅ تم إنشاء ${reportTypes[selectedReportType]} بنجاح!\n\nالفترة: من ${reportDateRange.startDate} إلى ${reportDateRange.endDate}\nعدد المشاريع: ${projects.length}\nإجمالي الميزانيات: ${projects.reduce((sum, p) => sum + p.budget, 0).toLocaleString('ar-EG')} ج.م\n\nسيتم تحميل التقرير تلقائياً...`)

      setShowReportModal(false)
    } catch (error) {
      alert('❌ حدث خطأ أثناء إنشاء التقرير')
    } finally {
      setIsGeneratingReport(false)
    }
  }

  const handleCancelReport = () => {
    setShowReportModal(false)
    setSelectedReportType('summary')
    setReportDateRange({
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    })
  }

  const tabs = [
    { id: 'projects', name: 'المشاريع', icon: '🏗️' },
    { id: 'cost-centers', name: 'مراكز التكلفة', icon: '💰' },
    { id: 'extracts', name: 'المستخلصات', icon: '📋' },
    { id: 'budget-analysis', name: 'تحليل الميزانية', icon: '📊' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                🏗️ إدارة المشاريع والمقاولات
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <EnhancedButton
                variant="primary"
                icon="➕"
                onClick={handleNewProject}
                disabled={isLoading}
              >
                مشروع جديد
              </EnhancedButton>
              <EnhancedButton
                variant="success"
                icon="📋"
                onClick={handleGenerateReport}
                disabled={isLoading || isGeneratingReport}
              >
                تقرير شامل
              </EnhancedButton>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              
              {/* المشاريع */}
              {activeTab === 'projects' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">🏗️ قائمة المشاريع</h2>
                  
                  {/* إحصائيات سريعة */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-blue-800 text-sm font-medium">إجمالي المشاريع</div>
                      <div className="text-blue-900 text-2xl font-bold">{projects.length}</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-green-800 text-sm font-medium">إجمالي الميزانيات</div>
                      <div className="text-green-900 text-lg font-bold">
                        {projects.reduce((sum, p) => sum + p.budget, 0).toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-yellow-800 text-sm font-medium">إجمالي المصروف</div>
                      <div className="text-yellow-900 text-lg font-bold">
                        {projects.reduce((sum, p) => sum + p.spent, 0).toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-purple-800 text-sm font-medium">متوسط التقدم</div>
                      <div className="text-purple-900 text-2xl font-bold">
                        {Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length)}%
                      </div>
                    </div>
                  </div>

                  {/* جدول المشاريع */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            رمز المشروع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            اسم المشروع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            العميل
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الحالة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            التقدم
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الميزانية
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المصروف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            إجراءات
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {projects.map((project) => (
                          <tr key={project.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                              {project.id}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {project.name}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {project.client}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(project.status)}`}>
                                {project.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                  <div 
                                    className={`h-2 rounded-full ${getProgressColor(project.progress)}`}
                                    style={{ width: `${project.progress}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium">{project.progress}%</span>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {project.budget.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {project.spent.toLocaleString('ar-EG')} ج.م
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => setSelectedProject(project)}
                                  className="text-blue-600 hover:text-blue-900 px-2 py-1 rounded"
                                >
                                  عرض
                                </button>
                                <button
                                  onClick={() => handleViewCostCenters(project)}
                                  className="text-purple-600 hover:text-purple-900 px-2 py-1 rounded bg-purple-50 hover:bg-purple-100"
                                >
                                  مراكز التكلفة
                                </button>
                                <button
                                  onClick={() => alert(`تعديل المشروع: ${project.name}`)}
                                  className="text-green-600 hover:text-green-900 px-2 py-1 rounded"
                                >
                                  تعديل
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* مراكز التكلفة */}
              {activeTab === 'cost-centers' && selectedProject && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">
                    💰 مراكز التكلفة - {selectedProject.name}
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {selectedProject.costCenters.map((center) => (
                      <div key={center.code} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-medium text-gray-900">{center.code}</h3>
                          <span className="text-sm text-gray-500">
                            {Math.round((center.spent / center.budget) * 100)}%
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{center.name}</p>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">الميزانية:</span>
                            <span className="font-medium">{center.budget.toLocaleString('ar-EG')} ج.م</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">المصروف:</span>
                            <span className="font-medium text-red-600">{center.spent.toLocaleString('ar-EG')} ج.م</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">المتبقي:</span>
                            <span className="font-medium text-green-600">
                              {(center.budget - center.spent).toLocaleString('ar-EG')} ج.م
                            </span>
                          </div>
                        </div>
                        
                        <div className="mt-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                (center.spent / center.budget) > 0.9 ? 'bg-red-500' :
                                (center.spent / center.budget) > 0.7 ? 'bg-yellow-500' : 'bg-green-500'
                              }`}
                              style={{ width: `${Math.min((center.spent / center.budget) * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* المستخلصات */}
              {activeTab === 'extracts' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📋 مستخلصات المشاريع</h2>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            رقم المستخلص
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المشروع
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            التاريخ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الوصف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            المبلغ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            الحالة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">
                            إجراءات
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {projectExtracts.map((extract) => {
                          const project = projects.find(p => p.id === extract.projectId)
                          return (
                            <tr key={extract.id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                                {extract.extractNumber}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900">
                                {project?.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {extract.date}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900">
                                {extract.description}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {extract.amount.toLocaleString('ar-EG')} ج.م
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(extract.status)}`}>
                                  {extract.status}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button 
                                  onClick={() => alert(`عرض تفاصيل المستخلص: ${extract.extractNumber}`)}
                                  className="text-blue-600 hover:text-blue-900 ml-4"
                                >
                                  عرض
                                </button>
                                <button 
                                  onClick={() => alert(`طباعة المستخلص: ${extract.extractNumber}`)}
                                  className="text-green-600 hover:text-green-900"
                                >
                                  طباعة
                                </button>
                              </td>
                            </tr>
                          )
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* تحليل الميزانية */}
              {activeTab === 'budget-analysis' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📊 تحليل انحراف الميزانية</h2>
                  
                  <div className="space-y-6">
                    {projects.map((project) => {
                      const variance = project.budget - project.spent
                      const variancePercent = ((variance / project.budget) * 100).toFixed(1)
                      
                      return (
                        <div key={project.id} className="border border-gray-200 rounded-lg p-6">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">{project.name}</h3>
                            <span className={`px-3 py-1 text-sm font-medium rounded-full ${
                              variance >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {variance >= 0 ? 'في الميزانية' : 'تجاوز الميزانية'}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="bg-blue-50 p-4 rounded-lg">
                              <div className="text-blue-800 text-sm font-medium">الميزانية المقررة</div>
                              <div className="text-blue-900 text-lg font-bold">
                                {project.budget.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                            <div className="bg-red-50 p-4 rounded-lg">
                              <div className="text-red-800 text-sm font-medium">المصروف الفعلي</div>
                              <div className="text-red-900 text-lg font-bold">
                                {project.spent.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                            <div className={`p-4 rounded-lg ${variance >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
                              <div className={`text-sm font-medium ${variance >= 0 ? 'text-green-800' : 'text-red-800'}`}>
                                الانحراف
                              </div>
                              <div className={`text-lg font-bold ${variance >= 0 ? 'text-green-900' : 'text-red-900'}`}>
                                {variance.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg">
                              <div className="text-gray-800 text-sm font-medium">نسبة الانحراف</div>
                              <div className={`text-lg font-bold ${variance >= 0 ? 'text-green-900' : 'text-red-900'}`}>
                                {variancePercent}%
                              </div>
                            </div>
                          </div>
                          
                          <div className="mt-4">
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                              <span>التقدم: {project.progress}%</span>
                              <span>المصروف: {((project.spent / project.budget) * 100).toFixed(1)}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-3">
                              <div className="relative h-3 rounded-full">
                                <div 
                                  className="absolute top-0 right-0 h-3 bg-blue-500 rounded-full"
                                  style={{ width: `${project.progress}%` }}
                                ></div>
                                <div 
                                  className="absolute top-0 right-0 h-3 bg-red-500 rounded-full opacity-70"
                                  style={{ width: `${Math.min((project.spent / project.budget) * 100, 100)}%` }}
                                ></div>
                              </div>
                            </div>
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                              <span>🔵 التقدم الفعلي</span>
                              <span>🔴 المصروف النسبي</span>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}

              {/* رسالة اختيار مشروع لمراكز التكلفة */}
              {activeTab === 'cost-centers' && !selectedProject && (
                <div className="text-center text-gray-500 py-12">
                  <div className="text-4xl mb-4">💰</div>
                  <h3 className="text-lg font-medium mb-2">اختر مشروعاً لعرض مراكز التكلفة</h3>
                  <p className="text-sm mb-4">انتقل إلى تبويب المشاريع واختر مشروعاً لعرض مراكز التكلفة الخاصة به</p>
                  <button
                    onClick={() => setActiveTab('projects')}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    عرض المشاريع
                  </button>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>

      {/* نموذج إضافة مشروع جديد */}
      {showNewProjectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">➕ إضافة مشروع جديد</h2>
                <button
                  onClick={handleCancelProject}
                  className="text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                {/* الصف الأول */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      كود المشروع *
                    </label>
                    <input
                      type="text"
                      value={newProject.projectCode}
                      onChange={(e) => setNewProject({...newProject, projectCode: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="PRJ-001"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      نوع المشروع
                    </label>
                    <select
                      value={newProject.projectType}
                      onChange={(e) => setNewProject({...newProject, projectType: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    >
                      <option value="civil">مدني</option>
                      <option value="structural">إنشائي</option>
                      <option value="finishing">تشطيبات</option>
                      <option value="electrical">كهرباء</option>
                      <option value="plumbing">سباكة</option>
                      <option value="infrastructure">بنية تحتية</option>
                    </select>
                  </div>
                </div>

                {/* الصف الثاني */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم المشروع *
                  </label>
                  <input
                    type="text"
                    value={newProject.projectName}
                    onChange={(e) => setNewProject({...newProject, projectName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="مشروع الفيلا السكنية"
                    disabled={isLoading}
                  />
                </div>

                {/* الصف الثالث */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      اسم العميل *
                    </label>
                    <input
                      type="text"
                      value={newProject.clientName}
                      onChange={(e) => setNewProject({...newProject, clientName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="أحمد علي محمد"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      رقم هاتف العميل
                    </label>
                    <input
                      type="text"
                      value={newProject.clientContact}
                      onChange={(e) => setNewProject({...newProject, clientContact: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="01234567890"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* الصف الرابع */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      تاريخ البداية
                    </label>
                    <input
                      type="date"
                      value={newProject.startDate}
                      onChange={(e) => setNewProject({...newProject, startDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      تاريخ الانتهاء المتوقع
                    </label>
                    <input
                      type="date"
                      value={newProject.endDate}
                      onChange={(e) => setNewProject({...newProject, endDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* الصف الخامس */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الميزانية المقدرة (جنيه مصري)
                    </label>
                    <input
                      type="number"
                      value={newProject.estimatedBudget}
                      onChange={(e) => setNewProject({...newProject, estimatedBudget: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="500000"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      موقع المشروع
                    </label>
                    <input
                      type="text"
                      value={newProject.location}
                      onChange={(e) => setNewProject({...newProject, location: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="التجمع الخامس، القاهرة الجديدة"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* وصف المشروع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    وصف المشروع
                  </label>
                  <textarea
                    value={newProject.description}
                    onChange={(e) => setNewProject({...newProject, description: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="وصف تفصيلي للمشروع..."
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* أزرار التحكم */}
              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
                <EnhancedButton
                  variant="ghost"
                  onClick={handleCancelProject}
                  disabled={isLoading}
                >
                  إلغاء
                </EnhancedButton>
                <EnhancedButton
                  variant="primary"
                  onClick={handleSaveProject}
                  disabled={isLoading}
                  icon={isLoading ? "⏳" : "💾"}
                >
                  {isLoading ? 'جاري الحفظ...' : 'حفظ المشروع'}
                </EnhancedButton>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نموذج إنشاء التقارير */}
      {showReportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">📊 إنشاء تقرير المشاريع</h2>
                <button
                  onClick={handleCancelReport}
                  className="text-gray-400 hover:text-gray-600"
                  disabled={isGeneratingReport}
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                {/* نوع التقرير */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع التقرير
                  </label>
                  <select
                    value={selectedReportType}
                    onChange={(e) => setSelectedReportType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isGeneratingReport}
                  >
                    <option value="summary">تقرير ملخص المشاريع</option>
                    <option value="detailed">تقرير تفصيلي للمشاريع</option>
                    <option value="financial">التقرير المالي للمشاريع</option>
                    <option value="progress">تقرير تقدم المشاريع</option>
                    <option value="costs">تقرير تكاليف المشاريع</option>
                  </select>
                </div>

                {/* نطاق التاريخ */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      من تاريخ
                    </label>
                    <input
                      type="date"
                      value={reportDateRange.startDate}
                      onChange={(e) => setReportDateRange({...reportDateRange, startDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isGeneratingReport}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      إلى تاريخ
                    </label>
                    <input
                      type="date"
                      value={reportDateRange.endDate}
                      onChange={(e) => setReportDateRange({...reportDateRange, endDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isGeneratingReport}
                    />
                  </div>
                </div>

                {/* معاينة التقرير */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">معاينة التقرير:</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>• عدد المشاريع: {projects.length}</div>
                    <div>• إجمالي الميزانيات: {projects.reduce((sum, p) => sum + p.budget, 0).toLocaleString('ar-EG')} ج.م</div>
                    <div>• المشاريع النشطة: {projects.filter(p => p.status === 'قيد التنفيذ').length}</div>
                    <div>• المشاريع المكتملة: {projects.filter(p => p.status === 'مكتمل').length}</div>
                  </div>
                </div>

                {/* رسالة التحميل */}
                {isGeneratingReport && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                      <span className="text-blue-800 text-sm">جاري إنشاء التقرير... يرجى الانتظار</span>
                    </div>
                  </div>
                )}
              </div>

              {/* أزرار التحكم */}
              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
                <EnhancedButton
                  variant="ghost"
                  onClick={handleCancelReport}
                  disabled={isGeneratingReport}
                >
                  إلغاء
                </EnhancedButton>
                <EnhancedButton
                  variant="success"
                  onClick={handleCreateReport}
                  disabled={isGeneratingReport}
                  icon={isGeneratingReport ? "⏳" : "📊"}
                >
                  {isGeneratingReport ? 'جاري الإنشاء...' : 'إنشاء التقرير'}
                </EnhancedButton>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نموذج عرض مراكز التكلفة */}
      {showCostCenterModal && selectedProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  💰 مراكز التكلفة - {selectedProject.name}
                </h2>
                <div className="flex items-center space-x-3">
                  <EnhancedButton
                    variant="primary"
                    icon="➕"
                    onClick={handleNewCostCenter}
                    disabled={isLoading}
                    size="sm"
                  >
                    مركز تكلفة جديد
                  </EnhancedButton>
                  <button
                    onClick={() => setShowCostCenterModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>
              </div>

              {/* إحصائيات سريعة */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                  <div className="text-3xl mb-2">📊</div>
                  <div className="text-blue-800 text-sm font-medium">إجمالي مراكز التكلفة</div>
                  <div className="text-blue-900 text-2xl font-bold">
                    {costCenters.filter(cc => cc.projectId === selectedProject.id).length}
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                  <div className="text-3xl mb-2">💰</div>
                  <div className="text-green-800 text-sm font-medium">إجمالي الميزانية</div>
                  <div className="text-green-900 text-lg font-bold">
                    {costCenters
                      .filter(cc => cc.projectId === selectedProject.id)
                      .reduce((sum, cc) => sum + cc.budget, 0)
                      .toLocaleString('ar-EG')} ج.م
                  </div>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                  <div className="text-3xl mb-2">📉</div>
                  <div className="text-red-800 text-sm font-medium">إجمالي المصروف</div>
                  <div className="text-red-900 text-lg font-bold">
                    {costCenters
                      .filter(cc => cc.projectId === selectedProject.id)
                      .reduce((sum, cc) => sum + cc.spent, 0)
                      .toLocaleString('ar-EG')} ج.م
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                  <div className="text-3xl mb-2">⚡</div>
                  <div className="text-yellow-800 text-sm font-medium">متوسط التقدم</div>
                  <div className="text-yellow-900 text-2xl font-bold">
                    {Math.round(
                      costCenters
                        .filter(cc => cc.projectId === selectedProject.id)
                        .reduce((sum, cc) => sum + cc.progress, 0) /
                      Math.max(costCenters.filter(cc => cc.projectId === selectedProject.id).length, 1)
                    )}%
                  </div>
                </div>
              </div>

              {/* جدول مراكز التكلفة */}
              <div className="space-y-4">
                {costCenters
                  .filter(cc => cc.projectId === selectedProject.id)
                  .map((costCenter) => (
                    <div key={costCenter.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-4">
                          <div>
                            <h4 className="font-medium text-gray-900">{costCenter.code}</h4>
                            <p className="text-sm text-gray-600">{costCenter.name}</p>
                          </div>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            costCenter.status === 'مكتمل' ? 'bg-green-100 text-green-800' :
                            costCenter.status === 'قيد التنفيذ' ? 'bg-blue-100 text-blue-800' :
                            costCenter.status === 'لم يبدأ' ? 'bg-gray-100 text-gray-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {costCenter.status}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">
                            {costCenter.progress}%
                          </div>
                          <div className="text-sm text-gray-500">
                            {costCenter.responsible}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                        <div>
                          <div className="text-sm text-gray-500">الميزانية</div>
                          <div className="font-medium">{costCenter.budget.toLocaleString('ar-EG')} ج.م</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">المصروف</div>
                          <div className="font-medium text-red-600">{costCenter.spent.toLocaleString('ar-EG')} ج.م</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">المتبقي</div>
                          <div className="font-medium text-green-600">{costCenter.remaining.toLocaleString('ar-EG')} ج.م</div>
                        </div>
                      </div>

                      <div className="mb-3">
                        <div className="flex justify-between text-sm mb-1">
                          <span>التقدم</span>
                          <span>{costCenter.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              costCenter.progress >= 80 ? 'bg-green-500' :
                              costCenter.progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${Math.min(costCenter.progress, 100)}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* المصروفات */}
                      {costCenter.expenses.length > 0 && (
                        <div className="mb-3">
                          <h5 className="text-sm font-medium text-gray-700 mb-2">آخر المصروفات:</h5>
                          <div className="space-y-1">
                            {costCenter.expenses.slice(-3).map((expense) => (
                              <div key={expense.id} className="flex justify-between text-xs text-gray-600">
                                <span>{expense.description}</span>
                                <span>{expense.amount.toLocaleString('ar-EG')} ج.م</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex justify-end space-x-2 flex-wrap gap-2">
                        <EnhancedButton
                          size="sm"
                          variant="ghost"
                          onClick={() => alert(`تفاصيل مركز التكلفة: ${costCenter.name}\n\nالوصف: ${costCenter.description}\nتاريخ البداية: ${costCenter.startDate}\nتاريخ النهاية: ${costCenter.endDate}`)}
                        >
                          عرض التفاصيل
                        </EnhancedButton>

                        <EnhancedButton
                          size="sm"
                          variant="primary"
                          onClick={() => handleAddExpenseToCostCenter(costCenter.id)}
                        >
                          إضافة مصروف
                        </EnhancedButton>

                        {costCenter.status === 'لم يبدأ' && (
                          <EnhancedButton
                            size="sm"
                            variant="success"
                            onClick={() => handleUpdateCostCenterStatus(costCenter.id, 'قيد التنفيذ')}
                          >
                            بدء التنفيذ
                          </EnhancedButton>
                        )}

                        {costCenter.status === 'قيد التنفيذ' && (
                          <EnhancedButton
                            size="sm"
                            variant="warning"
                            onClick={() => handleUpdateCostCenterStatus(costCenter.id, 'مكتمل')}
                          >
                            إكمال
                          </EnhancedButton>
                        )}
                      </div>
                    </div>
                  ))}
              </div>

              {costCenters.filter(cc => cc.projectId === selectedProject.id).length === 0 && (
                <div className="text-center py-8">
                  <div className="text-gray-400 text-6xl mb-4">📊</div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مراكز تكلفة</h3>
                  <p className="text-gray-500 mb-4">ابدأ بإضافة مركز تكلفة جديد لهذا المشروع</p>
                  <EnhancedButton
                    variant="primary"
                    icon="➕"
                    onClick={handleNewCostCenter}
                  >
                    إضافة مركز تكلفة جديد
                  </EnhancedButton>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* نموذج إضافة مركز تكلفة جديد */}
      {showNewCostCenterModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">➕ إضافة مركز تكلفة جديد</h2>
                <button
                  onClick={handleCancelCostCenter}
                  className="text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      كود مركز التكلفة *
                    </label>
                    <input
                      type="text"
                      value={newCostCenter.code}
                      onChange={(e) => setNewCostCenter({...newCostCenter, code: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="CC-001-06"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      المسؤول
                    </label>
                    <input
                      type="text"
                      value={newCostCenter.responsible}
                      onChange={(e) => setNewCostCenter({...newCostCenter, responsible: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="م. أحمد محمد"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم مركز التكلفة *
                  </label>
                  <input
                    type="text"
                    value={newCostCenter.name}
                    onChange={(e) => setNewCostCenter({...newCostCenter, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أعمال الدهانات والتشطيبات النهائية"
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الميزانية المخصصة (جنيه مصري) *
                  </label>
                  <input
                    type="number"
                    value={newCostCenter.budget}
                    onChange={(e) => setNewCostCenter({...newCostCenter, budget: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="50000"
                    min="1"
                    disabled={isLoading}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      تاريخ البداية
                    </label>
                    <input
                      type="date"
                      value={newCostCenter.startDate}
                      onChange={(e) => setNewCostCenter({...newCostCenter, startDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      تاريخ النهاية المتوقع
                    </label>
                    <input
                      type="date"
                      value={newCostCenter.endDate}
                      onChange={(e) => setNewCostCenter({...newCostCenter, endDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    وصف مركز التكلفة
                  </label>
                  <textarea
                    value={newCostCenter.description}
                    onChange={(e) => setNewCostCenter({...newCostCenter, description: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="وصف تفصيلي لأعمال مركز التكلفة..."
                    disabled={isLoading}
                  />
                </div>

                {newCostCenter.name && newCostCenter.budget && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">معاينة مركز التكلفة:</h4>
                    <div className="text-sm text-blue-800 space-y-1">
                      <div>• الكود: {newCostCenter.code}</div>
                      <div>• الاسم: {newCostCenter.name}</div>
                      <div>• الميزانية: {parseFloat(newCostCenter.budget || 0).toLocaleString('ar-EG')} ج.م</div>
                      <div>• المسؤول: {newCostCenter.responsible || 'غير محدد'}</div>
                      {newCostCenter.startDate && <div>• تاريخ البداية: {newCostCenter.startDate}</div>}
                    </div>
                  </div>
                )}

                {isLoading && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-3"></div>
                      <span className="text-green-800 text-sm">جاري إنشاء مركز التكلفة... يرجى الانتظار</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
                <EnhancedButton
                  variant="ghost"
                  onClick={handleCancelCostCenter}
                  disabled={isLoading}
                >
                  إلغاء
                </EnhancedButton>
                <EnhancedButton
                  variant="primary"
                  onClick={handleSaveCostCenter}
                  disabled={isLoading || !newCostCenter.code || !newCostCenter.name || !newCostCenter.budget}
                  icon={isLoading ? "⏳" : "💾"}
                >
                  {isLoading ? 'جاري الحفظ...' : 'حفظ مركز التكلفة'}
                </EnhancedButton>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProjectManagement
