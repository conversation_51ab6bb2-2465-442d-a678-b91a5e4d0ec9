const express = require('express');
const db = require('../database/connection');

const router = express.Router();

// Get financial reports
router.get('/financial-summary', async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        // Get revenue
        const revenue = await db.get(`
            SELECT COALESCE(SUM(credit_amount - debit_amount), 0) as total
            FROM general_ledger gl
            JOIN chart_of_accounts coa ON gl.account_id = coa.id
            WHERE coa.account_type = 'revenue'
            AND gl.transaction_date BETWEEN ? AND ?
        `, [startDate, endDate]);

        // Get expenses
        const expenses = await db.get(`
            SELECT COALESCE(SUM(debit_amount - credit_amount), 0) as total
            FROM general_ledger gl
            JOIN chart_of_accounts coa ON gl.account_id = coa.id
            WHERE coa.account_type = 'expense'
            AND gl.transaction_date BETWEEN ? AND ?
        `, [startDate, endDate]);

        res.json({
            success: true,
            data: {
                period: { startDate, endDate },
                revenue: revenue.total,
                expenses: expenses.total,
                netIncome: revenue.total - expenses.total
            }
        });
    } catch (error) {
        console.error('Get financial summary error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
