@echo off
title Construction ERP System

echo ========================================
echo    Construction ERP System
echo ========================================
echo.

echo Starting backend server...
cd server
start "Backend Server" cmd /k "node src/index.js"
cd ..

echo Waiting for server to start...
timeout /t 5 /nobreak >nul

echo Starting frontend client...
cd client  
start "Frontend Client" cmd /k "npm run dev"
cd ..

echo Waiting for client to start...
timeout /t 8 /nobreak >nul

echo Opening browser...
start http://localhost:5173

echo.
echo ========================================
echo System is starting...
echo Backend:  http://localhost:3001
echo Frontend: http://localhost:5173
echo Login:    admin / admin123
echo ========================================
echo.

pause
