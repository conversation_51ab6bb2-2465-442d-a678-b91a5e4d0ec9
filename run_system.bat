@echo off
chcp 65001 >nul
title نظام إدارة المقاولات - Construction ERP

echo.
echo ========================================
echo    🏗️ نظام إدارة المقاولات
echo    Construction ERP System
echo ========================================
echo.

echo 📋 التحقق من متطلبات النظام...

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo    تحميل من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
node --version

:: Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير مثبت
    pause
    exit /b 1
)

echo ✅ npm مثبت
npm --version

echo.
echo 📦 التحقق من التبعيات...

:: Check if server dependencies are installed
if not exist "server\node_modules" (
    echo 📥 تثبيت تبعيات الخادم...
    cd server
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات الخادم
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ تبعيات الخادم مثبتة
)

:: Check if client dependencies are installed
if not exist "client\node_modules" (
    echo 📥 تثبيت تبعيات العميل...
    cd client
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات العميل
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ تبعيات العميل مثبتة
)

echo.
echo 🗄️ إعداد قاعدة البيانات...

:: Check if database exists
if not exist "server\database\construction_erp.db" (
    echo 📊 إنشاء قاعدة البيانات...
    cd server
    node src/database/simple_migrate.js
    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
    
    echo 🌱 إضافة البيانات الأولية...
    node src/database/simple_seed.js
    if %errorlevel% neq 0 (
        echo ❌ فشل في إضافة البيانات الأولية
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ قاعدة البيانات موجودة
)

echo.
echo 🚀 تشغيل النظام...

:: Start backend server
echo 🔧 تشغيل خادم النظام...
cd server
start "خادم النظام - Backend Server" cmd /k "echo 🔧 خادم النظام يعمل على المنفذ 3001 && npm start"

:: Wait a moment for server to start
timeout /t 3 /nobreak >nul

:: Start frontend client
echo 🌐 تشغيل واجهة النظام...
cd ..\client
start "واجهة النظام - Frontend Client" cmd /k "echo 🌐 واجهة النظام تعمل على المنفذ 5173 && npm run dev"

:: Wait for services to start
echo ⏳ انتظار تشغيل الخدمات...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo ✅ تم تشغيل النظام بنجاح!
echo ========================================
echo.
echo 🌐 روابط الوصول:
echo    الواجهة الأمامية: http://localhost:5173
echo    خادم API:        http://localhost:3001
echo.
echo 🔑 بيانات الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور:   admin123
echo.
echo 📋 الوحدات المتاحة:
echo    • لوحة التحكم
echo    • المحاسبة العامة
echo    • إدارة المشاريع
echo    • المخزون
echo    • الفواتير والمدفوعات
echo    • الخزينة والبنوك
echo    • الموارد البشرية
echo    • الضرائب المصرية
echo    • التقارير
echo    • التحليلات الذكية
echo.
echo 💡 نصائح:
echo    • استخدم Ctrl+C لإيقاف الخدمات
echo    • تأكد من تغيير كلمة المرور الافتراضية
echo    • راجع ملف README_AR.md للمزيد من التفاصيل
echo.

:: Try to open browser automatically
echo 🌐 فتح المتصفح...
timeout /t 2 /nobreak >nul
start http://localhost:5173

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
