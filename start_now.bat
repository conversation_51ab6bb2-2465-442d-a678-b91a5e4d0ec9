@echo off
echo Starting Construction ERP System...

echo.
echo Starting backend server...
cd server
start "Backend" cmd /c "node test_server.js"
cd ..

echo Waiting 3 seconds...
timeout /t 3 /nobreak >nul

echo Starting frontend...
cd client
start "Frontend" cmd /c "npx vite --host 0.0.0.0 --port 5173"
cd ..

echo Waiting 5 seconds...
timeout /t 5 /nobreak >nul

echo Opening browser...
start http://localhost:5173

echo.
echo System started!
echo Backend: http://localhost:3001
echo Frontend: http://localhost:5173
echo Login: admin / admin123
echo.

pause
