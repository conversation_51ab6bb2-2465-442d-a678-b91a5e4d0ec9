import { forwardRef } from 'react'

const StatusBadge = forwardRef(({
  children,
  status = 'neutral',
  icon,
  className = '',
  ...props
}, ref) => {
  const statusClasses = {
    success: 'status-success',
    warning: 'status-warning',
    error: 'status-error',
    info: 'status-info',
    neutral: 'status-neutral'
  }

  const statusIcons = {
    success: '✅',
    warning: '⚠️',
    error: '❌',
    info: 'ℹ️',
    neutral: '⚪'
  }

  const classes = [
    'status-badge',
    statusClasses[status] || 'status-neutral',
    className
  ].filter(Boolean).join(' ')

  return (
    <span
      ref={ref}
      className={classes}
      {...props}
    >
      {icon || statusIcons[status]}
      {children}
    </span>
  )
})

StatusBadge.displayName = 'StatusBadge'

export default StatusBadge
