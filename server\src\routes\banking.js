const express = require('express');
const db = require('../database/connection');

const router = express.Router();

// Get bank accounts
router.get('/accounts', async (req, res) => {
    try {
        const accounts = await db.all(`
            SELECT 
                ba.*,
                coa.account_name as gl_account_name
            FROM bank_accounts ba
            LEFT JOIN chart_of_accounts coa ON ba.gl_account_id = coa.id
            WHERE ba.is_active = 1
            ORDER BY ba.account_name
        `);

        res.json({
            success: true,
            data: accounts
        });
    } catch (error) {
        console.error('Get bank accounts error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
