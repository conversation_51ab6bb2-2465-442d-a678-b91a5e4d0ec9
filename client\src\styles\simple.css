/* Enhanced Styles for Construction ERP System */

/* Root Variables */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;

  --border-color: #e2e8f0;
  --border-light: #f1f5f9;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);

  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  direction: rtl;
  text-align: right;
  background: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

/* Enhanced Card */
.enhanced-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
  overflow: hidden;
  position: relative;
}

.enhanced-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--border-color);
}

.enhanced-card.clickable {
  cursor: pointer;
}

.enhanced-card.clickable:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.enhanced-card.gradient {
  background: var(--bg-gradient);
  color: white;
}

.enhanced-card.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Card animations */
.enhanced-card.fade-in {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enhanced-card.slide-in {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Button */
.btn-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.15s ease-in-out;
  border: none;
  cursor: pointer;
  text-decoration: none;
  gap: 0.5rem;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.btn-secondary {
  background-color: #64748b;
  color: white;
}

.btn-secondary:hover {
  background-color: #475569;
  transform: translateY(-1px);
}

.btn-success {
  background-color: #10b981;
  color: white;
}

.btn-success:hover {
  background-color: #059669;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: #f59e0b;
  color: white;
}

.btn-warning:hover {
  background-color: #d97706;
  transform: translateY(-1px);
}

.btn-error {
  background-color: #ef4444;
  color: white;
}

.btn-error:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  gap: 0.25rem;
}

.status-success {
  background-color: rgb(220 252 231);
  color: rgb(22 101 52);
}

.status-warning {
  background-color: rgb(254 243 199);
  color: rgb(146 64 14);
}

.status-error {
  background-color: rgb(254 226 226);
  color: rgb(153 27 27);
}

.status-info {
  background-color: rgb(219 234 254);
  color: rgb(30 58 138);
}

.status-neutral {
  background-color: rgb(241 245 249);
  color: rgb(51 65 85);
}

/* Progress Bar */
.progress-enhanced {
  width: 100%;
  height: 0.5rem;
  background-color: #f1f5f9;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 9999px;
  transition: width 0.25s ease-in-out;
}

.progress-success {
  background-color: #10b981;
}

.progress-warning {
  background-color: #f59e0b;
}

.progress-error {
  background-color: #ef4444;
}

/* Input Enhanced */
.input-enhanced {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  background-color: white;
  color: #1e293b;
  font-size: 0.875rem;
  transition: all 0.15s ease-in-out;
}

.input-enhanced:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.input-enhanced:hover {
  border-color: #64748b;
}

/* Loading Spinner */
.spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Arabic Support */
body {
  font-family: 'Cairo', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  direction: rtl;
  text-align: right;
}

/* Enhanced Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Animation Classes */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.5s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

.animate-bounceIn {
  animation: bounceIn 0.8s ease-out;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Hover Effects */
.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Glass Effect */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .enhanced-card {
    margin: 0.5rem;
    border-radius: var(--radius-md);
  }

  .btn-enhanced {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  /* Reduce animations on mobile */
  .animate-fadeIn,
  .animate-slideIn,
  .animate-scaleIn,
  .animate-bounceIn {
    animation-duration: 0.3s;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;

    --border-color: #334155;
    --border-light: #475569;
  }

  .glass-card {
    background: rgba(30, 41, 59, 0.25);
    border: 1px solid rgba(203, 213, 225, 0.18);
  }
}

/* Print Styles */
@media print {
  .enhanced-card {
    box-shadow: none;
    border: 1px solid var(--border-color);
    break-inside: avoid;
  }

  .btn-enhanced {
    display: none;
  }

  .animate-fadeIn,
  .animate-slideIn,
  .animate-scaleIn,
  .animate-bounceIn {
    animation: none;
  }
}
