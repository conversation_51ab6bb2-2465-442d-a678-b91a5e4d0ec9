const express = require('express');
const { body, validationResult } = require('express-validator');
const db = require('../database/connection');
const { authorize } = require('../middleware/auth');

const router = express.Router();

// Get Chart of Accounts
router.get('/chart-of-accounts', async (req, res) => {
    try {
        const accounts = await db.all(`
            SELECT 
                coa.id,
                coa.account_code,
                coa.account_name,
                coa.account_type,
                coa.account_subtype,
                coa.parent_account_id,
                parent.account_name as parent_account_name,
                coa.is_active,
                coa.description,
                coa.created_at,
                coa.updated_at
            FROM chart_of_accounts coa
            LEFT JOIN chart_of_accounts parent ON coa.parent_account_id = parent.id
            ORDER BY coa.account_code
        `);

        res.json({
            success: true,
            data: accounts
        });

    } catch (error) {
        console.error('Get chart of accounts error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Create Account
router.post('/chart-of-accounts', authorize('admin', 'accountant'), [
    body('accountCode').notEmpty().withMessage('Account code is required'),
    body('accountName').notEmpty().withMessage('Account name is required'),
    body('accountType').isIn(['asset', 'liability', 'equity', 'revenue', 'expense'])
        .withMessage('Invalid account type')
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { accountCode, accountName, accountType, accountSubtype, parentAccountId, description } = req.body;

        // Check if account code already exists
        const existingAccount = await db.get(
            'SELECT id FROM chart_of_accounts WHERE account_code = ?',
            [accountCode]
        );

        if (existingAccount) {
            return res.status(400).json({
                success: false,
                message: 'Account code already exists'
            });
        }

        const result = await db.run(`
            INSERT INTO chart_of_accounts (
                account_code, account_name, account_type, account_subtype,
                parent_account_id, description, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, 1)
        `, [accountCode, accountName, accountType, accountSubtype, parentAccountId, description]);

        res.status(201).json({
            success: true,
            message: 'Account created successfully',
            data: {
                id: result.id,
                accountCode,
                accountName,
                accountType
            }
        });

    } catch (error) {
        console.error('Create account error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get General Ledger
router.get('/general-ledger', async (req, res) => {
    try {
        const { accountId, startDate, endDate, page = 1, limit = 50 } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = '1=1';
        let params = [];

        if (accountId) {
            whereClause += ' AND gl.account_id = ?';
            params.push(accountId);
        }

        if (startDate) {
            whereClause += ' AND gl.transaction_date >= ?';
            params.push(startDate);
        }

        if (endDate) {
            whereClause += ' AND gl.transaction_date <= ?';
            params.push(endDate);
        }

        const entries = await db.all(`
            SELECT 
                gl.id,
                gl.entry_number,
                gl.transaction_date,
                gl.account_id,
                coa.account_code,
                coa.account_name,
                gl.debit_amount,
                gl.credit_amount,
                gl.description,
                gl.reference_type,
                gl.reference_id,
                gl.project_id,
                p.project_name,
                u.first_name || ' ' || u.last_name as created_by_name,
                gl.created_at
            FROM general_ledger gl
            JOIN chart_of_accounts coa ON gl.account_id = coa.id
            LEFT JOIN projects p ON gl.project_id = p.id
            LEFT JOIN users u ON gl.created_by = u.id
            WHERE ${whereClause}
            ORDER BY gl.transaction_date DESC, gl.created_at DESC
            LIMIT ? OFFSET ?
        `, [...params, limit, offset]);

        // Get total count
        const countResult = await db.get(`
            SELECT COUNT(*) as total
            FROM general_ledger gl
            WHERE ${whereClause}
        `, params);

        res.json({
            success: true,
            data: {
                entries,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: countResult.total,
                    pages: Math.ceil(countResult.total / limit)
                }
            }
        });

    } catch (error) {
        console.error('Get general ledger error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Create General Ledger Entry
router.post('/general-ledger', authorize('admin', 'accountant'), [
    body('entryNumber').notEmpty().withMessage('Entry number is required'),
    body('transactionDate').isISO8601().withMessage('Valid transaction date is required'),
    body('entries').isArray({ min: 2 }).withMessage('At least 2 entries required for double-entry'),
    body('entries.*.accountId').isInt().withMessage('Account ID is required'),
    body('entries.*.debitAmount').optional().isFloat({ min: 0 }),
    body('entries.*.creditAmount').optional().isFloat({ min: 0 })
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { entryNumber, transactionDate, entries, description, projectId } = req.body;

        // Validate double-entry accounting (debits = credits)
        let totalDebits = 0;
        let totalCredits = 0;

        entries.forEach(entry => {
            totalDebits += parseFloat(entry.debitAmount || 0);
            totalCredits += parseFloat(entry.creditAmount || 0);
        });

        if (Math.abs(totalDebits - totalCredits) > 0.01) {
            return res.status(400).json({
                success: false,
                message: 'Debits must equal credits'
            });
        }

        // Check if entry number already exists
        const existingEntry = await db.get(
            'SELECT id FROM general_ledger WHERE entry_number = ?',
            [entryNumber]
        );

        if (existingEntry) {
            return res.status(400).json({
                success: false,
                message: 'Entry number already exists'
            });
        }

        // Create transaction queries
        const queries = entries.map(entry => ({
            sql: `
                INSERT INTO general_ledger (
                    entry_number, transaction_date, account_id, debit_amount,
                    credit_amount, description, project_id, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `,
            params: [
                entryNumber,
                transactionDate,
                entry.accountId,
                entry.debitAmount || 0,
                entry.creditAmount || 0,
                description,
                projectId,
                req.user.id
            ]
        }));

        // Execute transaction
        await db.transaction(queries);

        res.status(201).json({
            success: true,
            message: 'General ledger entry created successfully',
            data: {
                entryNumber,
                transactionDate,
                totalDebits,
                totalCredits
            }
        });

    } catch (error) {
        console.error('Create GL entry error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get Trial Balance
router.get('/trial-balance', async (req, res) => {
    try {
        const { asOfDate = new Date().toISOString().split('T')[0] } = req.query;

        const trialBalance = await db.all(`
            SELECT 
                coa.id,
                coa.account_code,
                coa.account_name,
                coa.account_type,
                COALESCE(SUM(gl.debit_amount), 0) as total_debits,
                COALESCE(SUM(gl.credit_amount), 0) as total_credits,
                CASE 
                    WHEN coa.account_type IN ('asset', 'expense') THEN 
                        COALESCE(SUM(gl.debit_amount), 0) - COALESCE(SUM(gl.credit_amount), 0)
                    ELSE 
                        COALESCE(SUM(gl.credit_amount), 0) - COALESCE(SUM(gl.debit_amount), 0)
                END as balance
            FROM chart_of_accounts coa
            LEFT JOIN general_ledger gl ON coa.id = gl.account_id 
                AND gl.transaction_date <= ?
            WHERE coa.is_active = 1
            GROUP BY coa.id, coa.account_code, coa.account_name, coa.account_type
            HAVING ABS(balance) > 0.01 OR coa.account_type IN ('asset', 'liability', 'equity')
            ORDER BY coa.account_code
        `, [asOfDate]);

        // Calculate totals
        let totalDebits = 0;
        let totalCredits = 0;

        trialBalance.forEach(account => {
            if (account.balance > 0) {
                if (account.account_type === 'asset' || account.account_type === 'expense') {
                    totalDebits += account.balance;
                } else {
                    totalCredits += account.balance;
                }
            } else if (account.balance < 0) {
                if (account.account_type === 'asset' || account.account_type === 'expense') {
                    totalCredits += Math.abs(account.balance);
                } else {
                    totalDebits += Math.abs(account.balance);
                }
            }
        });

        res.json({
            success: true,
            data: {
                asOfDate,
                accounts: trialBalance,
                totals: {
                    totalDebits,
                    totalCredits,
                    isBalanced: Math.abs(totalDebits - totalCredits) < 0.01
                }
            }
        });

    } catch (error) {
        console.error('Get trial balance error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
