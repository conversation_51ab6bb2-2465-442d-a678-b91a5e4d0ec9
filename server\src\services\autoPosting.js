// خدمة الترحيل التلقائي للحسابات العامة
const db = require('../database/connection');

class AutoPostingService {
    // ترحيل فاتورة مبيعات
    static async postSalesInvoice(invoiceData) {
        const { invoiceId, customerId, totalAmount, vatAmount, projectId, createdBy } = invoiceData;
        
        const entryNumber = `INV-${invoiceId}-${Date.now()}`;
        const transactionDate = new Date().toISOString().split('T')[0];
        
        // الحصول على أرقام الحسابات
        const customerAccount = await this.getAccountByCode('1210'); // عملاء تجاريون
        const salesAccount = await this.getAccountByCode('4110'); // إيرادات المقاولات
        const vatAccount = await this.getAccountByCode('2210'); // ضريبة القيمة المضافة
        
        const entries = [
            // مدين: العملاء
            {
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, project_id, created_by) VALUES (?, ?, ?, ?, 0, ?, 'invoice', ?, ?, ?)`,
                params: [entryNumber, transactionDate, customerAccount.id, totalAmount, 'فاتورة مبيعات', invoiceId, projectId, createdBy]
            },
            // دائن: المبيعات
            {
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, project_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'invoice', ?, ?, ?)`,
                params: [entryNumber, transactionDate, salesAccount.id, totalAmount - vatAmount, 'فاتورة مبيعات', invoiceId, projectId, createdBy]
            }
        ];
        
        // إضافة قيد ضريبة القيمة المضافة إذا وجدت
        if (vatAmount > 0) {
            entries.push({
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, project_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'invoice', ?, ?, ?)`,
                params: [entryNumber, transactionDate, vatAccount.id, vatAmount, 'ضريبة القيمة المضافة', invoiceId, projectId, createdBy]
            });
        }
        
        return await db.transaction(entries);
    }
    
    // ترحيل فاتورة مشتريات
    static async postPurchaseInvoice(invoiceData) {
        const { invoiceId, supplierId, totalAmount, vatAmount, projectId, createdBy } = invoiceData;
        
        const entryNumber = `PINV-${invoiceId}-${Date.now()}`;
        const transactionDate = new Date().toISOString().split('T')[0];
        
        const supplierAccount = await this.getAccountByCode('2110'); // موردون تجاريون
        const purchaseAccount = await this.getAccountByCode('5100'); // مواد مباشرة
        const vatAccount = await this.getAccountByCode('2210'); // ضريبة القيمة المضافة
        
        const entries = [
            // مدين: المشتريات
            {
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, project_id, created_by) VALUES (?, ?, ?, ?, 0, ?, 'invoice', ?, ?, ?)`,
                params: [entryNumber, transactionDate, purchaseAccount.id, totalAmount - vatAmount, 'فاتورة مشتريات', invoiceId, projectId, createdBy]
            },
            // دائن: الموردون
            {
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, project_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'invoice', ?, ?, ?)`,
                params: [entryNumber, transactionDate, supplierAccount.id, totalAmount, 'فاتورة مشتريات', invoiceId, projectId, createdBy]
            }
        ];
        
        // إضافة قيد ضريبة القيمة المضافة
        if (vatAmount > 0) {
            entries.push({
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, project_id, created_by) VALUES (?, ?, ?, ?, 0, ?, 'invoice', ?, ?, ?)`,
                params: [entryNumber, transactionDate, vatAccount.id, vatAmount, 'ضريبة القيمة المضافة', invoiceId, projectId, createdBy]
            });
        }
        
        return await db.transaction(entries);
    }
    
    // ترحيل دفعة
    static async postPayment(paymentData) {
        const { paymentId, paymentType, amount, bankAccountId, customerId, supplierId, createdBy } = paymentData;
        
        const entryNumber = `PAY-${paymentId}-${Date.now()}`;
        const transactionDate = new Date().toISOString().split('T')[0];
        
        const bankAccount = await this.getBankAccountGLId(bankAccountId);
        
        let entries = [];
        
        if (paymentType === 'receipt') {
            // مقبوض من عميل
            const customerAccount = await this.getAccountByCode('1210');
            entries = [
                // مدين: البنك
                {
                    sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, created_by) VALUES (?, ?, ?, ?, 0, ?, 'payment', ?, ?)`,
                    params: [entryNumber, transactionDate, bankAccount.gl_account_id, amount, 'مقبوض من عميل', paymentId, createdBy]
                },
                // دائن: العملاء
                {
                    sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'payment', ?, ?)`,
                    params: [entryNumber, transactionDate, customerAccount.id, amount, 'مقبوض من عميل', paymentId, createdBy]
                }
            ];
        } else {
            // مدفوع لمورد
            const supplierAccount = await this.getAccountByCode('2110');
            entries = [
                // مدين: الموردون
                {
                    sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, created_by) VALUES (?, ?, ?, ?, 0, ?, 'payment', ?, ?)`,
                    params: [entryNumber, transactionDate, supplierAccount.id, amount, 'مدفوع لمورد', paymentId, createdBy]
                },
                // دائن: البنك
                {
                    sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'payment', ?, ?)`,
                    params: [entryNumber, transactionDate, bankAccount.gl_account_id, amount, 'مدفوع لمورد', paymentId, createdBy]
                }
            ];
        }
        
        return await db.transaction(entries);
    }
    
    // ترحيل تكلفة مشروع
    static async postProjectCost(projectCostData) {
        const { projectId, costCenterId, transactionType, amount, description, createdBy } = projectCostData;
        
        const entryNumber = `PROJ-${projectId}-${Date.now()}`;
        const transactionDate = new Date().toISOString().split('T')[0];
        
        // تحديد الحساب حسب نوع التكلفة
        let expenseAccountCode;
        switch (transactionType) {
            case 'material':
                expenseAccountCode = '5100'; // مواد مباشرة
                break;
            case 'labor':
                expenseAccountCode = '5200'; // عمالة مباشرة
                break;
            case 'equipment':
                expenseAccountCode = '6300'; // إيجار معدات
                break;
            case 'overhead':
                expenseAccountCode = '6100'; // مصروفات إدارية
                break;
            case 'subcontractor':
                expenseAccountCode = '5300'; // مقاولو باطن
                break;
            default:
                expenseAccountCode = '6100';
        }
        
        const expenseAccount = await this.getAccountByCode(expenseAccountCode);
        const cashAccount = await this.getAccountByCode('1110'); // النقدية
        
        const entries = [
            // مدين: المصروف
            {
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, project_id, created_by) VALUES (?, ?, ?, ?, 0, ?, 'project_cost', ?, ?, ?)`,
                params: [entryNumber, transactionDate, expenseAccount.id, amount, description, projectId, projectId, createdBy]
            },
            // دائن: النقدية
            {
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, project_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'project_cost', ?, ?, ?)`,
                params: [entryNumber, transactionDate, cashAccount.id, amount, description, projectId, projectId, createdBy]
            }
        ];
        
        return await db.transaction(entries);
    }
    
    // ترحيل راتب موظف
    static async postPayroll(payrollData) {
        const { employeeId, netSalary, grossSalary, incomeTax, socialInsurance, createdBy } = payrollData;
        
        const entryNumber = `SAL-${employeeId}-${Date.now()}`;
        const transactionDate = new Date().toISOString().split('T')[0];
        
        const salaryExpenseAccount = await this.getAccountByCode('6100'); // مصروفات إدارية
        const salaryPayableAccount = await this.getAccountByCode('2310'); // رواتب مستحقة
        const taxPayableAccount = await this.getAccountByCode('2220'); // ضرائب مستحقة
        
        const entries = [
            // مدين: مصروف الراتب
            {
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, created_by) VALUES (?, ?, ?, ?, 0, ?, 'payroll', ?, ?)`,
                params: [entryNumber, transactionDate, salaryExpenseAccount.id, grossSalary, 'راتب موظف', employeeId, createdBy]
            },
            // دائن: رواتب مستحقة
            {
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'payroll', ?, ?)`,
                params: [entryNumber, transactionDate, salaryPayableAccount.id, netSalary, 'راتب موظف', employeeId, createdBy]
            }
        ];
        
        // إضافة ضريبة الدخل إذا وجدت
        if (incomeTax > 0) {
            entries.push({
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'payroll', ?, ?)`,
                params: [entryNumber, transactionDate, taxPayableAccount.id, incomeTax, 'ضريبة دخل موظف', employeeId, createdBy]
            });
        }
        
        // إضافة التأمينات الاجتماعية
        if (socialInsurance > 0) {
            entries.push({
                sql: `INSERT INTO general_ledger (entry_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_id, created_by) VALUES (?, ?, ?, 0, ?, ?, 'payroll', ?, ?)`,
                params: [entryNumber, transactionDate, taxPayableAccount.id, socialInsurance, 'تأمينات اجتماعية', employeeId, createdBy]
            });
        }
        
        return await db.transaction(entries);
    }
    
    // دوال مساعدة
    static async getAccountByCode(code) {
        return await db.get('SELECT id, account_name FROM chart_of_accounts WHERE account_code = ? AND is_active = 1', [code]);
    }
    
    static async getBankAccountGLId(bankAccountId) {
        return await db.get('SELECT gl_account_id FROM bank_accounts WHERE id = ?', [bankAccountId]);
    }
}

module.exports = AutoPostingService;
