# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_TYPE=sqlite
DB_PATH=./database/construction_erp.db
# For PostgreSQL (uncomment and configure if needed)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=construction_erp
# DB_USER=your_username
# DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Egyptian Tax Configuration
EGYPT_VAT_RATE=0.14
EGYPT_WITHHOLDING_TAX_RATE=0.10
EGYPT_INCOME_TAX_BRACKETS={"0":0,"30000":0.025,"45000":0.10,"200000":0.15,"400000":0.20,"800000":0.225,"1200000":0.25}

# AI Analytics Configuration
AI_SERVICE_URL=http://localhost:8000
AI_API_KEY=your-ai-api-key

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Backup Configuration
BACKUP_PATH=./backups
BACKUP_SCHEDULE=0 2 * * *
