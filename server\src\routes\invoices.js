const express = require('express');
const db = require('../database/connection');

const router = express.Router();

// Get invoices
router.get('/', async (req, res) => {
    try {
        const invoices = await db.all(`
            SELECT 
                i.*,
                CASE 
                    WHEN i.invoice_type = 'sales' THEN c.customer_name
                    ELSE s.supplier_name
                END as party_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            LEFT JOIN suppliers s ON i.supplier_id = s.id
            ORDER BY i.invoice_date DESC
            LIMIT 50
        `);

        res.json({
            success: true,
            data: invoices
        });
    } catch (error) {
        console.error('Get invoices error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
