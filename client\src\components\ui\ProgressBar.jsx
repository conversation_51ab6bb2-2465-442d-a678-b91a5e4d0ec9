import { forwardRef } from 'react'

const ProgressBar = forwardRef(({
  value = 0,
  max = 100,
  variant = 'success',
  size = 'md',
  showLabel = false,
  label,
  className = '',
  ...props
}, ref) => {
  const variants = {
    success: 'progress-success',
    warning: 'progress-warning',
    error: 'progress-error',
    info: 'bg-blue-500',
    neutral: 'bg-gray-500'
  }

  const sizes = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
    xl: 'h-4'
  }

  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)

  const containerClasses = [
    'progress-enhanced',
    sizes[size] || 'h-2',
    className
  ].filter(Boolean).join(' ')

  const barClasses = [
    'progress-bar',
    variants[variant] || 'progress-success'
  ].filter(Boolean).join(' ')

  return (
    <div ref={ref} className="w-full" {...props}>
      {showLabel && (
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium text-gray-700">
            {label || `${percentage.toFixed(0)}%`}
          </span>
          <span className="text-sm text-gray-500">
            {value} / {max}
          </span>
        </div>
      )}
      <div className={containerClasses}>
        <div
          className={barClasses}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  )
})

ProgressBar.displayName = 'ProgressBar'

export default ProgressBar
