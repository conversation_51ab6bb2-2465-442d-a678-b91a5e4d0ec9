import { useState } from 'react'
import { EnhancedCard, EnhancedButton, StatusBadge, ProgressBar } from '../../components/ui'

function InventoryManagement() {
  const [activeTab, setActiveTab] = useState('items')
  const [showNewItemModal, setShowNewItemModal] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // بيانات المواد
  const inventoryItems = [
    {
      id: 'ITM-001',
      name: 'أسمنت بورتلاندي',
      category: 'مواد بناء',
      unit: 'شيكارة',
      currentStock: 150,
      minStock: 50,
      maxStock: 500,
      unitPrice: 85,
      totalValue: 12750,
      supplier: 'شركة أسمنت طرة',
      location: 'مخزن رقم 1',
      status: 'متاح'
    },
    {
      id: 'ITM-002',
      name: 'حديد تسليح 12 مم',
      category: 'حديد',
      unit: 'طن',
      currentStock: 5,
      minStock: 10,
      maxStock: 50,
      unitPrice: 18500,
      totalValue: 92500,
      supplier: 'مصنع الحديد والصلب',
      location: 'مخزن رقم 2',
      status: 'منخفض'
    },
    {
      id: 'ITM-003',
      name: 'طوب أحمر',
      category: 'مواد بناء',
      unit: 'ألف طوبة',
      currentStock: 25,
      minStock: 15,
      maxStock: 100,
      unitPrice: 450,
      totalValue: 11250,
      supplier: 'مصنع الطوب الأحمر',
      location: 'مخزن رقم 1',
      status: 'متاح'
    }
  ]

  // بيانات حركات المخزون
  const stockMovements = [
    {
      id: 'MOV-001',
      itemId: 'ITM-001',
      itemName: 'أسمنت بورتلاندي',
      type: 'استلام',
      quantity: 100,
      date: '2024-01-15',
      reference: 'PO-001',
      notes: 'استلام من شركة أسمنت طرة'
    },
    {
      id: 'MOV-002',
      itemId: 'ITM-001',
      itemName: 'أسمنت بورتلاندي',
      type: 'صرف',
      quantity: -50,
      date: '2024-01-16',
      reference: 'PRJ-001',
      notes: 'صرف لمشروع الفيلا السكنية'
    }
  ]

  const [newItem, setNewItem] = useState({
    name: '',
    category: '',
    unit: '',
    minStock: '',
    maxStock: '',
    unitPrice: '',
    supplier: '',
    location: ''
  })

  const handleNewItem = () => {
    setShowNewItemModal(true)
  }

  const handleSaveItem = async () => {
    if (!newItem.name || !newItem.category || !newItem.unit) {
      alert('⚠️ يرجى ملء جميع الحقول المطلوبة')
      return
    }

    setIsLoading(true)
    
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      alert(`✅ تم إضافة الصنف بنجاح!\n\nالاسم: ${newItem.name}\nالفئة: ${newItem.category}\nالوحدة: ${newItem.unit}\nالحد الأدنى: ${newItem.minStock}\nالحد الأقصى: ${newItem.maxStock}`)
      
      setNewItem({
        name: '',
        category: '',
        unit: '',
        minStock: '',
        maxStock: '',
        unitPrice: '',
        supplier: '',
        location: ''
      })
      
      setShowNewItemModal(false)
    } catch (error) {
      alert('❌ حدث خطأ أثناء إضافة الصنف')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelItem = () => {
    setShowNewItemModal(false)
    setNewItem({
      name: '',
      category: '',
      unit: '',
      minStock: '',
      maxStock: '',
      unitPrice: '',
      supplier: '',
      location: ''
    })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'متاح': return 'bg-green-100 text-green-800'
      case 'منخفض': return 'bg-yellow-100 text-yellow-800'
      case 'نفد': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStockLevel = (current, min, max) => {
    if (current <= min) return 'منخفض'
    if (current >= max) return 'مرتفع'
    return 'طبيعي'
  }

  const tabs = [
    { id: 'items', name: 'الأصناف', icon: '📦' },
    { id: 'movements', name: 'حركات المخزون', icon: '📋' },
    { id: 'reports', name: 'التقارير', icon: '📊' },
    { id: 'alerts', name: 'التنبيهات', icon: '⚠️' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                📦 إدارة المخزون
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <EnhancedButton
                variant="primary"
                icon="➕"
                onClick={handleNewItem}
                disabled={isLoading}
              >
                صنف جديد
              </EnhancedButton>
              <EnhancedButton
                variant="success"
                icon="📊"
                onClick={() => {
                  const reportData = {
                    totalItems: inventoryItems.length,
                    totalValue: inventoryItems.reduce((sum, item) => sum + item.totalValue, 0),
                    lowStockItems: inventoryItems.filter(item => item.currentStock <= item.minStock).length,
                    categories: [...new Set(inventoryItems.map(item => item.category))].length
                  }
                  
                  alert(`📊 تقرير المخزون\n\n` +
                        `إجمالي الأصناف: ${reportData.totalItems}\n` +
                        `قيمة المخزون: ${reportData.totalValue.toLocaleString('ar-EG')} ج.م\n` +
                        `أصناف منخفضة: ${reportData.lowStockItems}\n` +
                        `عدد الفئات: ${reportData.categories}\n\n` +
                        `✅ تم إنشاء تقرير المخزون بنجاح!`)
                }}
              >
                تقرير المخزون
              </EnhancedButton>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-green-100 text-green-700 border-r-4 border-green-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">

              {/* الأصناف */}
              {activeTab === 'items' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📦 إدارة الأصناف</h2>

                  {/* إحصائيات سريعة */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <EnhancedCard className="text-center bg-blue-50 border-blue-200">
                      <div className="text-3xl mb-2">📦</div>
                      <div className="text-blue-800 text-sm font-medium">إجمالي الأصناف</div>
                      <div className="text-blue-900 text-2xl font-bold">
                        {inventoryItems.length}
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-green-50 border-green-200">
                      <div className="text-3xl mb-2">💰</div>
                      <div className="text-green-800 text-sm font-medium">قيمة المخزون</div>
                      <div className="text-green-900 text-lg font-bold">
                        {inventoryItems.reduce((sum, item) => sum + item.totalValue, 0).toLocaleString('ar-EG')} ج.م
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-yellow-50 border-yellow-200">
                      <div className="text-3xl mb-2">⚠️</div>
                      <div className="text-yellow-800 text-sm font-medium">أصناف منخفضة</div>
                      <div className="text-yellow-900 text-2xl font-bold">
                        {inventoryItems.filter(item => item.currentStock <= item.minStock).length}
                      </div>
                    </EnhancedCard>

                    <EnhancedCard className="text-center bg-purple-50 border-purple-200">
                      <div className="text-3xl mb-2">📂</div>
                      <div className="text-purple-800 text-sm font-medium">الفئات</div>
                      <div className="text-purple-900 text-2xl font-bold">
                        {[...new Set(inventoryItems.map(item => item.category))].length}
                      </div>
                    </EnhancedCard>
                  </div>

                  {/* جدول الأصناف */}
                  <EnhancedCard>
                    <div className="space-y-4">
                      {inventoryItems.map((item) => (
                        <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-4">
                              <div>
                                <h4 className="font-medium text-gray-900">{item.name}</h4>
                                <p className="text-sm text-gray-600">{item.id} - {item.category}</p>
                              </div>
                              <StatusBadge status={
                                item.status === 'متاح' ? 'success' :
                                item.status === 'منخفض' ? 'warning' : 'error'
                              }>
                                {item.status}
                              </StatusBadge>
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold text-gray-900">
                                {item.currentStock} {item.unit}
                              </div>
                              <div className="text-sm text-gray-500">
                                {item.totalValue.toLocaleString('ar-EG')} ج.م
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                            <div>
                              <div className="text-sm text-gray-500">المورد</div>
                              <div className="font-medium">{item.supplier}</div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-500">الموقع</div>
                              <div className="font-medium">{item.location}</div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-500">سعر الوحدة</div>
                              <div className="font-medium">{item.unitPrice.toLocaleString('ar-EG')} ج.م</div>
                            </div>
                          </div>

                          <div className="mb-3">
                            <div className="flex justify-between text-sm mb-1">
                              <span>مستوى المخزون</span>
                              <span>{((item.currentStock / item.maxStock) * 100).toFixed(0)}%</span>
                            </div>
                            <ProgressBar
                              value={(item.currentStock / item.maxStock) * 100}
                              variant={
                                item.currentStock <= item.minStock ? "error" :
                                item.currentStock <= item.minStock * 1.5 ? "warning" : "success"
                              }
                              size="sm"
                            />
                            <div className="flex justify-between text-xs text-gray-500 mt-1">
                              <span>الحد الأدنى: {item.minStock}</span>
                              <span>الحد الأقصى: {item.maxStock}</span>
                            </div>
                          </div>

                          <div className="flex justify-end space-x-2">
                            <EnhancedButton
                              size="sm"
                              variant="ghost"
                              onClick={() => alert(`عرض تفاصيل الصنف: ${item.name}`)}
                            >
                              عرض التفاصيل
                            </EnhancedButton>
                            <EnhancedButton
                              size="sm"
                              variant="primary"
                              onClick={() => alert(`تحديث مخزون: ${item.name}`)}
                            >
                              تحديث المخزون
                            </EnhancedButton>
                          </div>
                        </div>
                      ))}
                    </div>
                  </EnhancedCard>
                </div>
              )}

              {/* حركات المخزون */}
              {activeTab === 'movements' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📋 حركات المخزون</h2>

                  <EnhancedCard>
                    <div className="space-y-4">
                      {stockMovements.map((movement) => (
                        <div key={movement.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div>
                              <h4 className="font-medium text-gray-900">{movement.itemName}</h4>
                              <p className="text-sm text-gray-600">{movement.id} - {movement.reference}</p>
                            </div>
                            <div className="text-right">
                              <div className={`text-lg font-bold ${
                                movement.type === 'استلام' ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {movement.type === 'استلام' ? '+' : ''}{movement.quantity}
                              </div>
                              <div className="text-sm text-gray-500">{movement.date}</div>
                            </div>
                          </div>
                          <div className="text-sm text-gray-600">{movement.notes}</div>
                        </div>
                      ))}
                    </div>
                  </EnhancedCard>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>

      {/* نموذج إضافة صنف جديد */}
      {showNewItemModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">➕ إضافة صنف جديد</h2>
                <button
                  onClick={handleCancelItem}
                  className="text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      اسم الصنف *
                    </label>
                    <input
                      type="text"
                      value={newItem.name}
                      onChange={(e) => setNewItem({...newItem, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="أسمنت بورتلاندي"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الفئة *
                    </label>
                    <select
                      value={newItem.category}
                      onChange={(e) => setNewItem({...newItem, category: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      disabled={isLoading}
                    >
                      <option value="">اختر الفئة</option>
                      <option value="مواد بناء">مواد بناء</option>
                      <option value="حديد">حديد</option>
                      <option value="أدوات">أدوات</option>
                      <option value="كهرباء">كهرباء</option>
                      <option value="سباكة">سباكة</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الوحدة *
                    </label>
                    <input
                      type="text"
                      value={newItem.unit}
                      onChange={(e) => setNewItem({...newItem, unit: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="شيكارة"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الحد الأدنى
                    </label>
                    <input
                      type="number"
                      value={newItem.minStock}
                      onChange={(e) => setNewItem({...newItem, minStock: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="50"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      الحد الأقصى
                    </label>
                    <input
                      type="number"
                      value={newItem.maxStock}
                      onChange={(e) => setNewItem({...newItem, maxStock: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="500"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      سعر الوحدة (جنيه مصري)
                    </label>
                    <input
                      type="number"
                      value={newItem.unitPrice}
                      onChange={(e) => setNewItem({...newItem, unitPrice: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="85"
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      المورد
                    </label>
                    <input
                      type="text"
                      value={newItem.supplier}
                      onChange={(e) => setNewItem({...newItem, supplier: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      placeholder="شركة أسمنت طرة"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    موقع التخزين
                  </label>
                  <input
                    type="text"
                    value={newItem.location}
                    onChange={(e) => setNewItem({...newItem, location: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="مخزن رقم 1"
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
                <EnhancedButton
                  variant="ghost"
                  onClick={handleCancelItem}
                  disabled={isLoading}
                >
                  إلغاء
                </EnhancedButton>
                <EnhancedButton
                  variant="primary"
                  onClick={handleSaveItem}
                  disabled={isLoading}
                  icon={isLoading ? "⏳" : "💾"}
                >
                  {isLoading ? 'جاري الحفظ...' : 'حفظ الصنف'}
                </EnhancedButton>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default InventoryManagement
