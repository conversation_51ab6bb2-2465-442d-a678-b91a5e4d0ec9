import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import api from '../services/api'

const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // Initialize auth state
      initialize: async () => {
        const token = localStorage.getItem('token')
        if (token) {
          try {
            // Set token in API headers
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`

            // Simple check - if token exists, assume authenticated
            set({
              user: { username: 'admin', firstName: 'مدير', lastName: 'النظام' },
              token,
              isAuthenticated: true,
              isLoading: false
            })
          } catch (error) {
            // Token is invalid, clear it
            localStorage.removeItem('token')
            delete api.defaults.headers.common['Authorization']
            
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false
            })
          }
        } else {
          set({ isLoading: false })
        }
      },

      // Login
      login: async (credentials) => {
        try {
          const response = await api.post('/auth/login', credentials)
          const { token, user } = response.data.data

          // Store token
          localStorage.setItem('token', token)
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false
          })

          return { success: true }
        } catch (error) {
          const message = error.response?.data?.message || 'Login failed'
          return { success: false, message }
        }
      },

      // Logout
      logout: async () => {
        try {
          await api.post('/auth/logout')
        } catch (error) {
          // Continue with logout even if API call fails
          console.error('Logout API error:', error)
        }

        // Clear local storage and state
        localStorage.removeItem('token')
        delete api.defaults.headers.common['Authorization']

        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })
      },

      // Update user profile
      updateUser: (userData) => {
        set(state => ({
          user: { ...state.user, ...userData }
        }))
      },

      // Check if user has specific role
      hasRole: (role) => {
        const { user } = get()
        return user?.role === role
      },

      // Check if user has any of the specified roles
      hasAnyRole: (roles) => {
        const { user } = get()
        return roles.includes(user?.role)
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// Initialize auth state when store is created
useAuthStore.getState().initialize()

export { useAuthStore }
