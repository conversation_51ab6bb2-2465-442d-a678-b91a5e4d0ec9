import { useState } from 'react'

function FinancialStatements() {
  const [activeStatement, setActiveStatement] = useState('income')
  const [selectedPeriod, setSelectedPeriod] = useState('2024-01')
  const [comparisonPeriod, setComparisonPeriod] = useState('2023-01')
  const [showComparison, setShowComparison] = useState(true)

  // بيانات قائمة الدخل
  const incomeStatementData = {
    '2024-01': {
      revenues: {
        projectRevenues: 750000,
        otherRevenues: 50000,
        investmentRevenues: 25000
      },
      expenses: {
        directCosts: {
          materials: 300000,
          labor: 150000,
          equipment: 80000
        },
        operatingExpenses: {
          adminSalaries: 60000,
          rent: 24000,
          marketing: 15000,
          utilities: 12000,
          general: 8000
        }
      }
    },
    '2023-01': {
      revenues: {
        projectRevenues: 650000,
        otherRevenues: 40000,
        investmentRevenues: 20000
      },
      expenses: {
        directCosts: {
          materials: 280000,
          labor: 130000,
          equipment: 70000
        },
        operatingExpenses: {
          adminSalaries: 55000,
          rent: 24000,
          marketing: 12000,
          utilities: 10000,
          general: 7000
        }
      }
    }
  }

  // بيانات الميزانية العمومية
  const balanceSheetData = {
    '2024-01': {
      assets: {
        currentAssets: {
          cash: 48000,
          bank: 282000,
          receivables: 200000,
          inventory: 205000
        },
        fixedAssets: {
          land: 500000,
          equipment: 300000,
          vehicles: 150000,
          depreciation: -80000
        }
      },
      liabilities: {
        currentLiabilities: {
          payables: 95000,
          notes: 45000,
          accrued: 25000,
          taxes: 35000,
          salaries: 40000
        }
      },
      equity: {
        capital: 500000,
        reserves: 100000,
        retainedEarnings: 150000
      }
    }
  }

  // بيانات التدفقات النقدية
  const cashFlowData = {
    '2024-01': {
      operating: {
        netIncome: 196000,
        depreciation: 15000,
        receivablesChange: -25000,
        payablesChange: 15000,
        inventoryChange: -20000
      },
      investing: {
        equipmentPurchase: -50000,
        assetSale: 10000
      },
      financing: {
        loanProceeds: 100000,
        loanRepayment: -30000,
        dividends: -50000
      }
    }
  }

  const statements = [
    { id: 'income', name: 'قائمة الدخل', icon: '📊' },
    { id: 'balance', name: 'الميزانية العمومية', icon: '⚖️' },
    { id: 'cashflow', name: 'قائمة التدفقات النقدية', icon: '💰' },
    { id: 'ratios', name: 'النسب المالية', icon: '📈' }
  ]

  const calculateIncomeStatement = (period) => {
    const data = incomeStatementData[period]
    const totalRevenues = Object.values(data.revenues).reduce((sum, val) => sum + val, 0)
    const totalDirectCosts = Object.values(data.expenses.directCosts).reduce((sum, val) => sum + val, 0)
    const totalOperatingExpenses = Object.values(data.expenses.operatingExpenses).reduce((sum, val) => sum + val, 0)
    const grossProfit = totalRevenues - totalDirectCosts
    const netIncome = grossProfit - totalOperatingExpenses

    return {
      totalRevenues,
      totalDirectCosts,
      grossProfit,
      totalOperatingExpenses,
      netIncome,
      grossMargin: (grossProfit / totalRevenues) * 100,
      netMargin: (netIncome / totalRevenues) * 100
    }
  }

  const calculateBalanceSheet = (period) => {
    const data = balanceSheetData[period]
    const totalCurrentAssets = Object.values(data.assets.currentAssets).reduce((sum, val) => sum + val, 0)
    const totalFixedAssets = Object.values(data.assets.fixedAssets).reduce((sum, val) => sum + val, 0)
    const totalAssets = totalCurrentAssets + totalFixedAssets
    const totalLiabilities = Object.values(data.liabilities.currentLiabilities).reduce((sum, val) => sum + val, 0)
    const totalEquity = Object.values(data.equity).reduce((sum, val) => sum + val, 0)

    return {
      totalCurrentAssets,
      totalFixedAssets,
      totalAssets,
      totalLiabilities,
      totalEquity,
      workingCapital: totalCurrentAssets - totalLiabilities
    }
  }

  const calculateCashFlow = (period) => {
    const data = cashFlowData[period]
    const operatingCashFlow = Object.values(data.operating).reduce((sum, val) => sum + val, 0)
    const investingCashFlow = Object.values(data.investing).reduce((sum, val) => sum + val, 0)
    const financingCashFlow = Object.values(data.financing).reduce((sum, val) => sum + val, 0)
    const netCashFlow = operatingCashFlow + investingCashFlow + financingCashFlow

    return {
      operatingCashFlow,
      investingCashFlow,
      financingCashFlow,
      netCashFlow
    }
  }

  const calculateRatios = (period) => {
    const income = calculateIncomeStatement(period)
    const balance = calculateBalanceSheet(period)
    const balanceData = balanceSheetData[period]

    return {
      liquidity: {
        currentRatio: balance.totalCurrentAssets / balance.totalLiabilities,
        quickRatio: (balance.totalCurrentAssets - balanceData.assets.currentAssets.inventory) / balance.totalLiabilities
      },
      profitability: {
        grossMargin: income.grossMargin,
        netMargin: income.netMargin,
        roa: (income.netIncome / balance.totalAssets) * 100,
        roe: (income.netIncome / balance.totalEquity) * 100
      },
      leverage: {
        debtRatio: (balance.totalLiabilities / balance.totalAssets) * 100,
        equityRatio: (balance.totalEquity / balance.totalAssets) * 100
      }
    }
  }

  const formatCurrency = (amount) => {
    return amount.toLocaleString('ar-EG') + ' ج.م'
  }

  const formatPercentage = (percentage) => {
    return percentage.toFixed(1) + '%'
  }

  const getVarianceColor = (current, previous) => {
    if (current > previous) return 'text-green-600'
    if (current < previous) return 'text-red-600'
    return 'text-gray-600'
  }

  const currentIncome = calculateIncomeStatement(selectedPeriod)
  const previousIncome = showComparison ? calculateIncomeStatement(comparisonPeriod) : null
  const currentBalance = calculateBalanceSheet(selectedPeriod)
  const currentCashFlow = calculateCashFlow(selectedPeriod)
  const currentRatios = calculateRatios(selectedPeriod)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                📊 القوائم المالية المتقدمة
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="2024-01">يناير 2024</option>
                <option value="2023-01">يناير 2023</option>
              </select>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={showComparison}
                  onChange={(e) => setShowComparison(e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm">مقارنة</span>
              </label>

              <button
                onClick={() => alert('📊 جاري تصدير القوائم المالية...')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📋 تصدير PDF
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex">
          {/* Sidebar */}
          <div className="w-64 bg-white rounded-lg shadow-sm p-4 h-fit">
            <nav className="space-y-2">
              {statements.map((statement) => (
                <button
                  key={statement.id}
                  onClick={() => setActiveStatement(statement.id)}
                  className={`w-full text-right px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    activeStatement === statement.id
                      ? 'bg-blue-100 text-blue-700 border-r-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="ml-3">{statement.icon}</span>
                  {statement.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 mr-6">
            <div className="bg-white rounded-lg shadow-sm p-6">

              {/* قائمة الدخل */}
              {activeStatement === 'income' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">📊 قائمة الدخل</h2>

                  <div className="overflow-x-auto">
                    <table className="min-w-full">
                      <thead>
                        <tr className="border-b-2 border-gray-200">
                          <th className="text-right py-3 text-sm font-medium text-gray-700">البيان</th>
                          <th className="text-right py-3 text-sm font-medium text-gray-700">{selectedPeriod}</th>
                          {showComparison && (
                            <>
                              <th className="text-right py-3 text-sm font-medium text-gray-700">{comparisonPeriod}</th>
                              <th className="text-right py-3 text-sm font-medium text-gray-700">التغيير</th>
                              <th className="text-right py-3 text-sm font-medium text-gray-700">النسبة</th>
                            </>
                          )}
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-100">
                        {/* الإيرادات */}
                        <tr className="bg-green-50">
                          <td className="py-3 font-medium text-green-800">الإيرادات</td>
                          <td className="py-3 font-medium text-green-800">{formatCurrency(currentIncome.totalRevenues)}</td>
                          {showComparison && (
                            <>
                              <td className="py-3 font-medium text-green-800">{formatCurrency(previousIncome.totalRevenues)}</td>
                              <td className={`py-3 font-medium ${getVarianceColor(currentIncome.totalRevenues, previousIncome.totalRevenues)}`}>
                                {formatCurrency(currentIncome.totalRevenues - previousIncome.totalRevenues)}
                              </td>
                              <td className={`py-3 font-medium ${getVarianceColor(currentIncome.totalRevenues, previousIncome.totalRevenues)}`}>
                                {formatPercentage(((currentIncome.totalRevenues - previousIncome.totalRevenues) / previousIncome.totalRevenues) * 100)}
                              </td>
                            </>
                          )}
                        </tr>

                        <tr>
                          <td className="py-2 pr-4">إيرادات المشاريع</td>
                          <td className="py-2">{formatCurrency(incomeStatementData[selectedPeriod].revenues.projectRevenues)}</td>
                          {showComparison && (
                            <>
                              <td className="py-2">{formatCurrency(incomeStatementData[comparisonPeriod].revenues.projectRevenues)}</td>
                              <td className="py-2">-</td>
                              <td className="py-2">-</td>
                            </>
                          )}
                        </tr>

                        <tr>
                          <td className="py-2 pr-4">إيرادات أخرى</td>
                          <td className="py-2">{formatCurrency(incomeStatementData[selectedPeriod].revenues.otherRevenues)}</td>
                          {showComparison && (
                            <>
                              <td className="py-2">{formatCurrency(incomeStatementData[comparisonPeriod].revenues.otherRevenues)}</td>
                              <td className="py-2">-</td>
                              <td className="py-2">-</td>
                            </>
                          )}
                        </tr>

                        {/* التكاليف المباشرة */}
                        <tr className="bg-red-50">
                          <td className="py-3 font-medium text-red-800">التكاليف المباشرة</td>
                          <td className="py-3 font-medium text-red-800">({formatCurrency(currentIncome.totalDirectCosts)})</td>
                          {showComparison && (
                            <>
                              <td className="py-3 font-medium text-red-800">({formatCurrency(previousIncome.totalDirectCosts)})</td>
                              <td className={`py-3 font-medium ${getVarianceColor(previousIncome.totalDirectCosts, currentIncome.totalDirectCosts)}`}>
                                ({formatCurrency(currentIncome.totalDirectCosts - previousIncome.totalDirectCosts)})
                              </td>
                              <td className={`py-3 font-medium ${getVarianceColor(previousIncome.totalDirectCosts, currentIncome.totalDirectCosts)}`}>
                                {formatPercentage(((currentIncome.totalDirectCosts - previousIncome.totalDirectCosts) / previousIncome.totalDirectCosts) * 100)}
                              </td>
                            </>
                          )}
                        </tr>

                        {/* إجمالي الربح */}
                        <tr className="bg-blue-50 border-t-2 border-blue-200">
                          <td className="py-3 font-bold text-blue-800">إجمالي الربح</td>
                          <td className="py-3 font-bold text-blue-800">{formatCurrency(currentIncome.grossProfit)}</td>
                          {showComparison && (
                            <>
                              <td className="py-3 font-bold text-blue-800">{formatCurrency(previousIncome.grossProfit)}</td>
                              <td className={`py-3 font-bold ${getVarianceColor(currentIncome.grossProfit, previousIncome.grossProfit)}`}>
                                {formatCurrency(currentIncome.grossProfit - previousIncome.grossProfit)}
                              </td>
                              <td className={`py-3 font-bold ${getVarianceColor(currentIncome.grossProfit, previousIncome.grossProfit)}`}>
                                {formatPercentage(((currentIncome.grossProfit - previousIncome.grossProfit) / previousIncome.grossProfit) * 100)}
                              </td>
                            </>
                          )}
                        </tr>

                        {/* المصروفات التشغيلية */}
                        <tr className="bg-purple-50">
                          <td className="py-3 font-medium text-purple-800">المصروفات التشغيلية</td>
                          <td className="py-3 font-medium text-purple-800">({formatCurrency(currentIncome.totalOperatingExpenses)})</td>
                          {showComparison && (
                            <>
                              <td className="py-3 font-medium text-purple-800">({formatCurrency(previousIncome.totalOperatingExpenses)})</td>
                              <td className={`py-3 font-medium ${getVarianceColor(previousIncome.totalOperatingExpenses, currentIncome.totalOperatingExpenses)}`}>
                                ({formatCurrency(currentIncome.totalOperatingExpenses - previousIncome.totalOperatingExpenses)})
                              </td>
                              <td className={`py-3 font-medium ${getVarianceColor(previousIncome.totalOperatingExpenses, currentIncome.totalOperatingExpenses)}`}>
                                {formatPercentage(((currentIncome.totalOperatingExpenses - previousIncome.totalOperatingExpenses) / previousIncome.totalOperatingExpenses) * 100)}
                              </td>
                            </>
                          )}
                        </tr>

                        {/* صافي الربح */}
                        <tr className="bg-yellow-50 border-t-2 border-yellow-200">
                          <td className="py-4 font-bold text-yellow-800 text-lg">صافي الربح</td>
                          <td className="py-4 font-bold text-yellow-800 text-lg">{formatCurrency(currentIncome.netIncome)}</td>
                          {showComparison && (
                            <>
                              <td className="py-4 font-bold text-yellow-800 text-lg">{formatCurrency(previousIncome.netIncome)}</td>
                              <td className={`py-4 font-bold text-lg ${getVarianceColor(currentIncome.netIncome, previousIncome.netIncome)}`}>
                                {formatCurrency(currentIncome.netIncome - previousIncome.netIncome)}
                              </td>
                              <td className={`py-4 font-bold text-lg ${getVarianceColor(currentIncome.netIncome, previousIncome.netIncome)}`}>
                                {formatPercentage(((currentIncome.netIncome - previousIncome.netIncome) / previousIncome.netIncome) * 100)}
                              </td>
                            </>
                          )}
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  {/* النسب المالية السريعة */}
                  <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-green-800 text-sm font-medium">هامش الربح الإجمالي</div>
                      <div className="text-green-900 text-2xl font-bold">{formatPercentage(currentIncome.grossMargin)}</div>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-blue-800 text-sm font-medium">هامش الربح الصافي</div>
                      <div className="text-blue-900 text-2xl font-bold">{formatPercentage(currentIncome.netMargin)}</div>
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="text-yellow-800 text-sm font-medium">نمو الإيرادات</div>
                      <div className="text-yellow-900 text-2xl font-bold">
                        {showComparison ?
                          formatPercentage(((currentIncome.totalRevenues - previousIncome.totalRevenues) / previousIncome.totalRevenues) * 100) :
                          'N/A'
                        }
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* الميزانية العمومية */}
              {activeStatement === 'balance' && (
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-6">⚖️ الميزانية العمومية</h2>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* الأصول */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="text-lg font-medium text-green-800 mb-4">💰 الأصول</h3>

                      <div className="space-y-3">
                        <div className="bg-green-50 p-3 rounded">
                          <h4 className="font-medium text-green-700 mb-2">الأصول المتداولة</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>النقدية بالصندوق</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].assets.currentAssets.cash)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>النقدية بالبنوك</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].assets.currentAssets.bank)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>حسابات العملاء</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].assets.currentAssets.receivables)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>المخزون</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].assets.currentAssets.inventory)}</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 font-medium">
                              <span>إجمالي الأصول المتداولة</span>
                              <span>{formatCurrency(currentBalance.totalCurrentAssets)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-green-100 p-3 rounded">
                          <h4 className="font-medium text-green-700 mb-2">الأصول الثابتة</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>الأراضي والمباني</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].assets.fixedAssets.land)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>المعدات والآلات</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].assets.fixedAssets.equipment)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>وسائل النقل</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].assets.fixedAssets.vehicles)}</span>
                            </div>
                            <div className="flex justify-between text-red-600">
                              <span>مجمع الإهلاك</span>
                              <span>({formatCurrency(Math.abs(balanceSheetData[selectedPeriod].assets.fixedAssets.depreciation))})</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 font-medium">
                              <span>صافي الأصول الثابتة</span>
                              <span>{formatCurrency(currentBalance.totalFixedAssets)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-green-200 p-3 rounded">
                          <div className="flex justify-between font-bold text-green-800">
                            <span>إجمالي الأصول</span>
                            <span>{formatCurrency(currentBalance.totalAssets)}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* الخصوم وحقوق الملكية */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="text-lg font-medium text-red-800 mb-4">📋 الخصوم وحقوق الملكية</h3>

                      <div className="space-y-3">
                        <div className="bg-red-50 p-3 rounded">
                          <h4 className="font-medium text-red-700 mb-2">الخصوم المتداولة</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>حسابات الموردين</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].liabilities.currentLiabilities.payables)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>أوراق الدفع</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].liabilities.currentLiabilities.notes)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>مصروفات مستحقة</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].liabilities.currentLiabilities.accrued)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>ضرائب مستحقة</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].liabilities.currentLiabilities.taxes)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>رواتب مستحقة</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].liabilities.currentLiabilities.salaries)}</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 font-medium">
                              <span>إجمالي الخصوم</span>
                              <span>{formatCurrency(currentBalance.totalLiabilities)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-blue-50 p-3 rounded">
                          <h4 className="font-medium text-blue-700 mb-2">حقوق الملكية</h4>
                          <div className="space-y-1 text-sm">
                            <div className="flex justify-between">
                              <span>رأس المال</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].equity.capital)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>الاحتياطيات</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].equity.reserves)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>الأرباح المحتجزة</span>
                              <span>{formatCurrency(balanceSheetData[selectedPeriod].equity.retainedEarnings)}</span>
                            </div>
                            <div className="flex justify-between border-t pt-1 font-medium">
                              <span>إجمالي حقوق الملكية</span>
                              <span>{formatCurrency(currentBalance.totalEquity)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gray-200 p-3 rounded">
                          <div className="flex justify-between font-bold text-gray-800">
                            <span>إجمالي الخصوم وحقوق الملكية</span>
                            <span>{formatCurrency(currentBalance.totalLiabilities + currentBalance.totalEquity)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* مؤشرات سريعة */}
                  <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-blue-800 text-sm font-medium">رأس المال العامل</div>
                      <div className="text-blue-900 text-2xl font-bold">{formatCurrency(currentBalance.workingCapital)}</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-green-800 text-sm font-medium">نسبة الأصول الثابتة</div>
                      <div className="text-green-900 text-2xl font-bold">
                        {formatPercentage((currentBalance.totalFixedAssets / currentBalance.totalAssets) * 100)}
                      </div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-purple-800 text-sm font-medium">نسبة حقوق الملكية</div>
                      <div className="text-purple-900 text-2xl font-bold">
                        {formatPercentage((currentBalance.totalEquity / currentBalance.totalAssets) * 100)}
                      </div>
                    </div>
                  </div>
                </div>
              )}

            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FinancialStatements