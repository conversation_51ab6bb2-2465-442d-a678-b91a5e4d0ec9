import { useState } from 'react'

function ChartOfAccounts() {
  const [expandedNodes, setExpandedNodes] = useState(new Set(['1000', '2000', '3000', '4000', '5000']))
  const [selectedAccount, setSelectedAccount] = useState(null)

  // شجرة الحسابات الهيكلية
  const accountsTree = [
    {
      code: '1000',
      name: 'الأصول',
      type: 'asset',
      level: 0,
      children: [
        {
          code: '1100',
          name: 'الأصول المتداولة',
          type: 'asset',
          level: 1,
          children: [
            { code: '1110', name: 'النقدية بالصندوق', type: 'asset', level: 2, balance: 50000 },
            { code: '1120', name: 'النقدية بالبنوك', type: 'asset', level: 2, balance: 250000 },
            { code: '1130', name: 'حسابات العملاء', type: 'asset', level: 2, balance: 180000 },
            { code: '1140', name: 'أوراق القبض', type: 'asset', level: 2, balance: 75000 },
            { code: '1150', name: 'المخزون - مواد خام', type: 'asset', level: 2, balance: 120000 },
            { code: '1160', name: 'المخزون - مواد تحت التشغيل', type: 'asset', level: 2, balance: 85000 }
          ]
        },
        {
          code: '1200',
          name: 'الأصول الثابتة',
          type: 'asset',
          level: 1,
          children: [
            { code: '1210', name: 'الأراضي والمباني', type: 'asset', level: 2, balance: 500000 },
            { code: '1220', name: 'المعدات والآلات', type: 'asset', level: 2, balance: 300000 },
            { code: '1230', name: 'وسائل النقل', type: 'asset', level: 2, balance: 150000 },
            { code: '1240', name: 'مجمع الإهلاك', type: 'asset', level: 2, balance: -80000 }
          ]
        }
      ]
    },
    {
      code: '2000',
      name: 'الخصوم',
      type: 'liability',
      level: 0,
      children: [
        {
          code: '2100',
          name: 'الخصوم المتداولة',
          type: 'liability',
          level: 1,
          children: [
            { code: '2110', name: 'حسابات الموردين', type: 'liability', level: 2, balance: 95000 },
            { code: '2120', name: 'أوراق الدفع', type: 'liability', level: 2, balance: 45000 },
            { code: '2130', name: 'مصروفات مستحقة', type: 'liability', level: 2, balance: 25000 },
            { code: '2140', name: 'ضرائب مستحقة', type: 'liability', level: 2, balance: 35000 },
            { code: '2150', name: 'رواتب مستحقة', type: 'liability', level: 2, balance: 40000 }
          ]
        }
      ]
    },
    {
      code: '3000',
      name: 'حقوق الملكية',
      type: 'equity',
      level: 0,
      children: [
        { code: '3100', name: 'رأس المال', type: 'equity', level: 1, balance: 500000 },
        { code: '3200', name: 'الاحتياطيات', type: 'equity', level: 1, balance: 100000 },
        { code: '3300', name: 'الأرباح المحتجزة', type: 'equity', level: 1, balance: 150000 }
      ]
    },
    {
      code: '4000',
      name: 'الإيرادات',
      type: 'revenue',
      level: 0,
      children: [
        { code: '4100', name: 'إيرادات المشاريع', type: 'revenue', level: 1, balance: 750000 },
        { code: '4200', name: 'إيرادات أخرى', type: 'revenue', level: 1, balance: 50000 },
        { code: '4300', name: 'إيرادات استثمارية', type: 'revenue', level: 1, balance: 25000 }
      ]
    },
    {
      code: '5000',
      name: 'المصروفات',
      type: 'expense',
      level: 0,
      children: [
        {
          code: '5100',
          name: 'تكلفة المشاريع المباشرة',
          type: 'expense',
          level: 1,
          children: [
            { code: '5110', name: 'مواد البناء', type: 'expense', level: 2, balance: 300000 },
            { code: '5120', name: 'أجور العمال', type: 'expense', level: 2, balance: 150000 },
            { code: '5130', name: 'معدات وآلات', type: 'expense', level: 2, balance: 80000 }
          ]
        },
        {
          code: '5200',
          name: 'المصروفات العمومية',
          type: 'expense',
          level: 1,
          children: [
            { code: '5210', name: 'رواتب إدارية', type: 'expense', level: 2, balance: 60000 },
            { code: '5220', name: 'إيجارات', type: 'expense', level: 2, balance: 24000 },
            { code: '5230', name: 'مصروفات تسويق', type: 'expense', level: 2, balance: 15000 }
          ]
        }
      ]
    }
  ]

  const toggleNode = (code) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(code)) {
      newExpanded.delete(code)
    } else {
      newExpanded.add(code)
    }
    setExpandedNodes(newExpanded)
  }

  const getAccountTypeColor = (type) => {
    switch (type) {
      case 'asset': return 'text-green-600'
      case 'liability': return 'text-red-600'
      case 'equity': return 'text-blue-600'
      case 'revenue': return 'text-yellow-600'
      case 'expense': return 'text-purple-600'
      default: return 'text-gray-600'
    }
  }

  const getAccountTypeIcon = (type) => {
    switch (type) {
      case 'asset': return '💰'
      case 'liability': return '📋'
      case 'equity': return '🏛️'
      case 'revenue': return '📈'
      case 'expense': return '📉'
      default: return '📄'
    }
  }

  const renderAccountNode = (account, parentCode = '') => {
    const hasChildren = account.children && account.children.length > 0
    const isExpanded = expandedNodes.has(account.code)
    const indentLevel = account.level * 20

    return (
      <div key={account.code} className="select-none">
        <div 
          className={`flex items-center py-2 px-3 hover:bg-gray-50 cursor-pointer border-r-4 ${
            selectedAccount?.code === account.code ? 'bg-blue-50 border-blue-500' : 'border-transparent'
          }`}
          style={{ paddingRight: `${indentLevel + 12}px` }}
          onClick={() => setSelectedAccount(account)}
        >
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                toggleNode(account.code)
              }}
              className="mr-2 text-gray-400 hover:text-gray-600"
            >
              {isExpanded ? '▼' : '▶'}
            </button>
          )}
          {!hasChildren && <span className="mr-6"></span>}
          
          <span className="mr-2">{getAccountTypeIcon(account.type)}</span>
          
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div>
                <span className="font-medium text-gray-900">{account.code}</span>
                <span className="mr-3 text-gray-700">{account.name}</span>
              </div>
              {account.balance !== undefined && (
                <span className={`text-sm font-medium ${getAccountTypeColor(account.type)}`}>
                  {account.balance.toLocaleString('ar-EG')} ج.م
                </span>
              )}
            </div>
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div>
            {account.children.map(child => renderAccountNode(child, account.code))}
          </div>
        )}
      </div>
    )
  }

  const calculateTotalByType = (type) => {
    let total = 0
    const traverse = (accounts) => {
      accounts.forEach(account => {
        if (account.type === type && account.balance !== undefined) {
          total += account.balance
        }
        if (account.children) {
          traverse(account.children)
        }
      })
    }
    traverse(accountsTree)
    return total
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.back()}
                className="ml-4 text-gray-600 hover:text-gray-900"
              >
                ← العودة
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                📋 شجرة الحسابات الديناميكية
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => {
                  const accountData = {
                    code: prompt('كود الحساب:'),
                    name: prompt('اسم الحساب:'),
                    type: prompt('نوع الحساب (asset/liability/equity/revenue/expense):')
                  }

                  if (accountData.code && accountData.name && accountData.type) {
                    alert(`✅ تم إنشاء الحساب بنجاح!\n\nالكود: ${accountData.code}\nالاسم: ${accountData.name}\nالنوع: ${accountData.type}`)
                  }
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                ➕ حساب جديد
              </button>
              <button
                onClick={() => alert('📊 جاري تصدير شجرة الحسابات...')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                📋 تصدير
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* شجرة الحسابات */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow-sm">
            <div className="p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">🌳 شجرة الحسابات</h2>
              
              {/* ملخص الأرصدة */}
              <div className="grid grid-cols-5 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl mb-1">💰</div>
                  <div className="text-xs text-gray-500">الأصول</div>
                  <div className="text-sm font-medium text-green-600">
                    {calculateTotalByType('asset').toLocaleString('ar-EG')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-1">📋</div>
                  <div className="text-xs text-gray-500">الخصوم</div>
                  <div className="text-sm font-medium text-red-600">
                    {calculateTotalByType('liability').toLocaleString('ar-EG')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-1">🏛️</div>
                  <div className="text-xs text-gray-500">حقوق الملكية</div>
                  <div className="text-sm font-medium text-blue-600">
                    {calculateTotalByType('equity').toLocaleString('ar-EG')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-1">📈</div>
                  <div className="text-xs text-gray-500">الإيرادات</div>
                  <div className="text-sm font-medium text-yellow-600">
                    {calculateTotalByType('revenue').toLocaleString('ar-EG')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl mb-1">📉</div>
                  <div className="text-xs text-gray-500">المصروفات</div>
                  <div className="text-sm font-medium text-purple-600">
                    {calculateTotalByType('expense').toLocaleString('ar-EG')}
                  </div>
                </div>
              </div>

              {/* الشجرة */}
              <div className="border border-gray-200 rounded-lg max-h-96 overflow-y-auto">
                {accountsTree.map(account => renderAccountNode(account))}
              </div>
            </div>
          </div>

          {/* تفاصيل الحساب المحدد */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">📊 تفاصيل الحساب</h3>
              
              {selectedAccount ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">رمز الحساب</label>
                    <div className="mt-1 text-lg font-mono">{selectedAccount.code}</div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">اسم الحساب</label>
                    <div className="mt-1">{selectedAccount.name}</div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">نوع الحساب</label>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        selectedAccount.type === 'asset' ? 'bg-green-100 text-green-800' :
                        selectedAccount.type === 'liability' ? 'bg-red-100 text-red-800' :
                        selectedAccount.type === 'equity' ? 'bg-blue-100 text-blue-800' :
                        selectedAccount.type === 'revenue' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {getAccountTypeIcon(selectedAccount.type)} {
                          selectedAccount.type === 'asset' ? 'أصول' :
                          selectedAccount.type === 'liability' ? 'خصوم' :
                          selectedAccount.type === 'equity' ? 'حقوق ملكية' :
                          selectedAccount.type === 'revenue' ? 'إيرادات' : 'مصروفات'
                        }
                      </span>
                    </div>
                  </div>
                  
                  {selectedAccount.balance !== undefined && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">الرصيد الحالي</label>
                      <div className={`mt-1 text-xl font-bold ${getAccountTypeColor(selectedAccount.type)}`}>
                        {selectedAccount.balance.toLocaleString('ar-EG')} ج.م
                      </div>
                    </div>
                  )}
                  
                  <div className="pt-4 space-y-2">
                    <button 
                      onClick={() => alert(`عرض كشف حساب: ${selectedAccount.name}`)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      📋 كشف الحساب
                    </button>
                    <button 
                      onClick={() => alert(`تعديل الحساب: ${selectedAccount.name}`)}
                      className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      ✏️ تعديل
                    </button>
                    <button 
                      onClick={() => alert(`حذف الحساب: ${selectedAccount.name}`)}
                      className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      🗑️ حذف
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <div className="text-4xl mb-2">📋</div>
                  <p>اختر حساباً من الشجرة لعرض التفاصيل</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChartOfAccounts
