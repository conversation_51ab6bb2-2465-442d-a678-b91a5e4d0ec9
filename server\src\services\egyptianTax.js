// خدمة الضرائب المصرية
const db = require('../database/connection');

class EgyptianTaxService {
    // حساب ضريبة القيمة المضافة
    static calculateVAT(amount, vatRate = 0.14) {
        const vatAmount = amount * vatRate;
        return {
            taxableAmount: amount,
            vatRate: vatRate,
            vatAmount: parseFloat(vatAmount.toFixed(2)),
            totalAmount: parseFloat((amount + vatAmount).toFixed(2))
        };
    }
    
    // حساب ضريبة الخصم والإضافة
    static calculateWithholdingTax(grossAmount, withholdingRate = 0.10) {
        const withholdingAmount = grossAmount * withholdingRate;
        return {
            grossAmount: grossAmount,
            withholdingRate: withholdingRate,
            withholdingAmount: parseFloat(withholdingAmount.toFixed(2)),
            netAmount: parseFloat((grossAmount - withholdingAmount).toFixed(2))
        };
    }
    
    // حساب ضريبة الدخل على الشركات
    static calculateCorporateIncomeTax(taxableIncome) {
        // الشرائح الضريبية المصرية للشركات
        const taxBrackets = [
            { min: 0, max: 30000, rate: 0 },
            { min: 30000, max: 45000, rate: 0.025 },
            { min: 45000, max: 200000, rate: 0.10 },
            { min: 200000, max: 400000, rate: 0.15 },
            { min: 400000, max: 800000, rate: 0.20 },
            { min: 800000, max: 1200000, rate: 0.225 },
            { min: 1200000, max: Infinity, rate: 0.25 }
        ];
        
        let totalTax = 0;
        let remainingIncome = taxableIncome;
        
        for (const bracket of taxBrackets) {
            if (remainingIncome <= 0) break;
            
            const taxableInBracket = Math.min(remainingIncome, bracket.max - bracket.min);
            const taxInBracket = taxableInBracket * bracket.rate;
            
            totalTax += taxInBracket;
            remainingIncome -= taxableInBracket;
        }
        
        return {
            taxableIncome: taxableIncome,
            incomeTax: parseFloat(totalTax.toFixed(2)),
            netIncome: parseFloat((taxableIncome - totalTax).toFixed(2)),
            effectiveRate: taxableIncome > 0 ? parseFloat((totalTax / taxableIncome * 100).toFixed(2)) : 0
        };
    }
    
    // تسجيل معاملة ضريبة القيمة المضافة
    static async recordVATTransaction(vatData) {
        const {
            transactionDate,
            transactionType, // 'sales' or 'purchase'
            invoiceId,
            taxableAmount,
            vatRate,
            vatAmount,
            customerTaxId,
            supplierTaxId,
            glEntryId
        } = vatData;
        
        return await db.run(`
            INSERT INTO vat_transactions (
                transaction_date, transaction_type, invoice_id, taxable_amount,
                vat_rate, vat_amount, customer_tax_id, supplier_tax_id, gl_entry_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            transactionDate, transactionType, invoiceId, taxableAmount,
            vatRate, vatAmount, customerTaxId, supplierTaxId, glEntryId
        ]);
    }
    
    // تسجيل معاملة ضريبة الخصم والإضافة
    static async recordWithholdingTaxTransaction(withholdingData) {
        const {
            transactionDate,
            supplierId,
            invoiceId,
            grossAmount,
            withholdingRate,
            withholdingAmount,
            netAmount,
            certificateNumber,
            glEntryId
        } = withholdingData;
        
        return await db.run(`
            INSERT INTO withholding_tax_transactions (
                transaction_date, supplier_id, invoice_id, gross_amount,
                withholding_rate, withholding_amount, net_amount,
                certificate_number, gl_entry_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
            transactionDate, supplierId, invoiceId, grossAmount,
            withholdingRate, withholdingAmount, netAmount,
            certificateNumber, glEntryId
        ]);
    }
    
    // إنشاء تقرير ضريبة القيمة المضافة
    static async generateVATReport(startDate, endDate) {
        const vatTransactions = await db.all(`
            SELECT 
                vt.*,
                i.invoice_number,
                CASE 
                    WHEN vt.transaction_type = 'sales' THEN c.customer_name
                    ELSE s.supplier_name
                END as party_name
            FROM vat_transactions vt
            LEFT JOIN invoices i ON vt.invoice_id = i.id
            LEFT JOIN customers c ON i.customer_id = c.id
            LEFT JOIN suppliers s ON i.supplier_id = s.id
            WHERE vt.transaction_date BETWEEN ? AND ?
            ORDER BY vt.transaction_date DESC
        `, [startDate, endDate]);
        
        // حساب الإجماليات
        const summary = {
            totalSalesVAT: 0,
            totalPurchaseVAT: 0,
            netVATDue: 0,
            transactionCount: vatTransactions.length
        };
        
        vatTransactions.forEach(transaction => {
            if (transaction.transaction_type === 'sales') {
                summary.totalSalesVAT += transaction.vat_amount;
            } else {
                summary.totalPurchaseVAT += transaction.vat_amount;
            }
        });
        
        summary.netVATDue = summary.totalSalesVAT - summary.totalPurchaseVAT;
        
        return {
            period: { startDate, endDate },
            transactions: vatTransactions,
            summary: summary
        };
    }
    
    // إنشاء تقرير ضريبة الخصم والإضافة
    static async generateWithholdingTaxReport(startDate, endDate) {
        const withholdingTransactions = await db.all(`
            SELECT 
                wt.*,
                s.supplier_name,
                s.tax_id as supplier_tax_id,
                i.invoice_number
            FROM withholding_tax_transactions wt
            JOIN suppliers s ON wt.supplier_id = s.id
            LEFT JOIN invoices i ON wt.invoice_id = i.id
            WHERE wt.transaction_date BETWEEN ? AND ?
            ORDER BY wt.transaction_date DESC
        `, [startDate, endDate]);
        
        const summary = {
            totalGrossAmount: 0,
            totalWithholdingAmount: 0,
            totalNetAmount: 0,
            transactionCount: withholdingTransactions.length
        };
        
        withholdingTransactions.forEach(transaction => {
            summary.totalGrossAmount += transaction.gross_amount;
            summary.totalWithholdingAmount += transaction.withholding_amount;
            summary.totalNetAmount += transaction.net_amount;
        });
        
        return {
            period: { startDate, endDate },
            transactions: withholdingTransactions,
            summary: summary
        };
    }
    
    // إنشاء نموذج 41 (تقرير ضريبة القيمة المضافة الشهري)
    static async generateForm41(year, month) {
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        const endDate = new Date(year, month, 0).toISOString().split('T')[0]; // آخر يوم في الشهر
        
        // الحصول على بيانات المبيعات والمشتريات
        const salesData = await db.all(`
            SELECT SUM(taxable_amount) as total_sales, SUM(vat_amount) as output_vat
            FROM vat_transactions 
            WHERE transaction_type = 'sales' 
            AND transaction_date BETWEEN ? AND ?
        `, [startDate, endDate]);
        
        const purchaseData = await db.all(`
            SELECT SUM(taxable_amount) as total_purchases, SUM(vat_amount) as input_vat
            FROM vat_transactions 
            WHERE transaction_type = 'purchase' 
            AND transaction_date BETWEEN ? AND ?
        `, [startDate, endDate]);
        
        const totalSales = salesData[0]?.total_sales || 0;
        const outputVAT = salesData[0]?.output_vat || 0;
        const totalPurchases = purchaseData[0]?.total_purchases || 0;
        const inputVAT = purchaseData[0]?.input_vat || 0;
        const netVATDue = outputVAT - inputVAT;
        
        const submissionPeriod = `${year}-${month.toString().padStart(2, '0')}`;
        
        // حفظ أو تحديث نموذج 41
        await db.run(`
            INSERT OR REPLACE INTO form_41_submissions (
                submission_period, total_sales, total_purchases,
                output_vat, input_vat, net_vat_due, status, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, 'draft', 1)
        `, [submissionPeriod, totalSales, totalPurchases, outputVAT, inputVAT, netVATDue]);
        
        return {
            submissionPeriod,
            totalSales,
            totalPurchases,
            outputVAT,
            inputVAT,
            netVATDue,
            status: 'draft'
        };
    }
    
    // الحصول على الإعدادات الضريبية الحالية
    static async getCurrentTaxSettings() {
        return await db.all(`
            SELECT * FROM tax_settings 
            WHERE is_active = 1 
            ORDER BY tax_type, effective_date DESC
        `);
    }
    
    // تحديث معدل ضريبي
    static async updateTaxRate(taxType, newRate, effectiveDate, description) {
        // إلغاء تفعيل المعدل القديم
        await db.run(`
            UPDATE tax_settings 
            SET is_active = 0 
            WHERE tax_type = ? AND is_active = 1
        `, [taxType]);
        
        // إضافة المعدل الجديد
        return await db.run(`
            INSERT INTO tax_settings (tax_type, tax_rate, effective_date, description, is_active)
            VALUES (?, ?, ?, ?, 1)
        `, [taxType, newRate, effectiveDate, description]);
    }
}

module.exports = EgyptianTaxService;
