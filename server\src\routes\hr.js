const express = require('express');
const db = require('../database/connection');

const router = express.Router();

// Get employees
router.get('/employees', async (req, res) => {
    try {
        const employees = await db.all(`
            SELECT 
                e.*,
                u.username
            FROM employees e
            LEFT JOIN users u ON e.user_id = u.id
            WHERE e.is_active = 1
            ORDER BY e.first_name, e.last_name
        `);

        res.json({
            success: true,
            data: employees
        });
    } catch (error) {
        console.error('Get employees error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
