@echo off
title Construction ERP - Manual Start
color 0E

echo ========================================
echo    Construction ERP System
echo    Manual Start Guide
echo ========================================
echo.

echo 📋 تعليمات التشغيل اليدوي:
echo.

echo 1️⃣ تأكد من تثبيت Node.js:
echo    - اذهب إلى: https://nodejs.org
echo    - حمل النسخة LTS
echo    - قم بالتثبيت مع الإعدادات الافتراضية
echo.

echo 2️⃣ افتح نافذتين من Command Prompt:
echo    - اضغط Win+R واكتب cmd
echo    - كرر العملية لفتح نافذة ثانية
echo.

echo 3️⃣ في النافذة الأولى (الخادم):
echo    cd /d "%~dp0server"
echo    npm install
echo    npm start
echo.

echo 4️⃣ في النافذة الثانية (الواجهة):
echo    cd /d "%~dp0client"
echo    npm install
echo    npm run dev
echo.

echo 5️⃣ افتح المتصفح واذهب إلى:
echo    http://localhost:5173
echo.

echo 🔑 بيانات الدخول:
echo    المستخدم: admin
echo    كلمة المرور: admin123
echo.

echo ========================================
echo.

echo هل تريد فتح نوافذ Command Prompt الآن؟
echo اضغط Y للموافقة أو أي مفتاح آخر للخروج
choice /c YN /n /m "اختر (Y/N): "

if %errorlevel% equ 1 (
    echo.
    echo فتح نوافذ Command Prompt...
    
    :: Open first command prompt for server
    start "خادم النظام - Server" cmd /k "echo تشغيل خادم النظام && echo. && echo cd /d \"%~dp0server\" && echo npm install && echo npm start && echo."
    
    :: Wait a moment
    timeout /t 2 /nobreak >nul
    
    :: Open second command prompt for client
    start "واجهة النظام - Client" cmd /k "echo تشغيل واجهة النظام && echo. && echo cd /d \"%~dp0client\" && echo npm install && echo npm run dev && echo."
    
    echo.
    echo تم فتح نوافذ Command Prompt
    echo اتبع التعليمات في كل نافذة
)

echo.
pause
