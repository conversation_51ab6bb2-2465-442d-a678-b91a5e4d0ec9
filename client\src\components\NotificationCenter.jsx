import { useState, useEffect, Fragment } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { Menu, Transition } from '@headlessui/react'
import { 
  BellIcon, 
  CheckIcon, 
  XMarkIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import api from '../services/api'
import { getTimeAgo } from '../utils/formatters'

function NotificationCenter() {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()

  // الحصول على الإشعارات
  const { data: notifications = [], isLoading } = useQuery(
    'notifications',
    () => api.get('/notifications?limit=20').then(res => res.data.data),
    {
      refetchInterval: 30000, // تحديث كل 30 ثانية
    }
  )

  // الحصول على عدد الإشعارات غير المقروءة
  const { data: unreadCount = 0 } = useQuery(
    'notifications-unread-count',
    () => api.get('/notifications/unread-count').then(res => res.data.data.count),
    {
      refetchInterval: 30000,
    }
  )

  // تحديد إشعار كمقروء
  const markAsReadMutation = useMutation(
    (notificationId) => api.put(`/notifications/${notificationId}/read`),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications')
        queryClient.invalidateQueries('notifications-unread-count')
      }
    }
  )

  // تحديد جميع الإشعارات كمقروءة
  const markAllAsReadMutation = useMutation(
    () => api.put('/notifications/mark-all-read'),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications')
        queryClient.invalidateQueries('notifications-unread-count')
      }
    }
  )

  // حذف إشعار
  const deleteNotificationMutation = useMutation(
    (notificationId) => api.delete(`/notifications/${notificationId}`),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('notifications')
        queryClient.invalidateQueries('notifications-unread-count')
      }
    }
  )

  const getNotificationIcon = (type) => {
    const iconClass = "h-5 w-5"
    
    switch (type) {
      case 'success':
        return <CheckCircleIcon className={`${iconClass} text-green-500`} />
      case 'warning':
        return <ExclamationTriangleIcon className={`${iconClass} text-yellow-500`} />
      case 'error':
        return <XCircleIcon className={`${iconClass} text-red-500`} />
      default:
        return <InformationCircleIcon className={`${iconClass} text-blue-500`} />
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'border-r-4 border-red-500'
      case 'high':
        return 'border-r-4 border-orange-500'
      case 'medium':
        return 'border-r-4 border-yellow-500'
      default:
        return 'border-r-4 border-blue-500'
    }
  }

  const handleNotificationClick = (notification) => {
    if (!notification.is_read) {
      markAsReadMutation.mutate(notification.id)
    }
    
    if (notification.action_url) {
      // التنقل للصفحة المحددة
      window.location.href = notification.action_url
    }
  }

  return (
    <Menu as="div" className="relative">
      <Menu.Button className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full">
        <BellIcon className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute left-0 mt-2 w-96 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">الإشعارات</h3>
              {unreadCount > 0 && (
                <button
                  onClick={() => markAllAsReadMutation.mutate()}
                  className="text-sm text-primary-600 hover:text-primary-800"
                  disabled={markAllAsReadMutation.isLoading}
                >
                  تحديد الكل كمقروء
                </button>
              )}
            </div>

            <div className="max-h-96 overflow-y-auto">
              {isLoading ? (
                <div className="flex justify-center py-4">
                  <div className="spinner h-6 w-6"></div>
                </div>
              ) : notifications.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <BellIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>لا توجد إشعارات</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        notification.is_read 
                          ? 'bg-gray-50 hover:bg-gray-100' 
                          : 'bg-blue-50 hover:bg-blue-100'
                      } ${getPriorityColor(notification.priority)}`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start space-x-3 space-x-reverse">
                        <div className="flex-shrink-0">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className={`text-sm font-medium ${
                              notification.is_read ? 'text-gray-700' : 'text-gray-900'
                            }`}>
                              {notification.title}
                            </p>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                deleteNotificationMutation.mutate(notification.id)
                              }}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                          
                          <p className={`text-sm mt-1 ${
                            notification.is_read ? 'text-gray-500' : 'text-gray-700'
                          }`}>
                            {notification.message}
                          </p>
                          
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-400">
                              {getTimeAgo(notification.created_at)}
                            </span>
                            
                            {notification.priority === 'urgent' && (
                              <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                                عاجل
                              </span>
                            )}
                            
                            {notification.priority === 'high' && (
                              <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                                مهم
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {notifications.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <button className="w-full text-center text-sm text-primary-600 hover:text-primary-800">
                  عرض جميع الإشعارات
                </button>
              </div>
            )}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  )
}

export default NotificationCenter
