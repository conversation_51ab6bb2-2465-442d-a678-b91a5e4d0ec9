const express = require('express');
const { authMiddleware } = require('../middleware/auth');
const NotificationService = require('../services/notificationService');

const router = express.Router();

// الحصول على إشعارات المستخدم
router.get('/', authMiddleware, async (req, res) => {
    try {
        const { isRead, type, limit = 50, offset = 0 } = req.query;
        
        const options = {
            limit: parseInt(limit),
            offset: parseInt(offset)
        };

        if (isRead !== undefined) {
            options.isRead = isRead === 'true';
        }

        if (type) {
            options.type = type;
        }

        const notifications = await NotificationService.getUserNotifications(
            req.user.id,
            options
        );

        res.json({
            success: true,
            data: notifications
        });

    } catch (error) {
        console.error('Get notifications error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// الحصول على عدد الإشعارات غير المقروءة
router.get('/unread-count', authMiddleware, async (req, res) => {
    try {
        const count = await NotificationService.getUnreadCount(req.user.id);

        res.json({
            success: true,
            data: { count }
        });

    } catch (error) {
        console.error('Get unread count error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحديد إشعار كمقروء
router.put('/:id/read', authMiddleware, async (req, res) => {
    try {
        const success = await NotificationService.markAsRead(
            req.params.id,
            req.user.id
        );

        if (success) {
            res.json({
                success: true,
                message: 'Notification marked as read'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

    } catch (error) {
        console.error('Mark as read error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تحديد جميع الإشعارات كمقروءة
router.put('/mark-all-read', authMiddleware, async (req, res) => {
    try {
        await NotificationService.markAllAsRead(req.user.id);

        res.json({
            success: true,
            message: 'All notifications marked as read'
        });

    } catch (error) {
        console.error('Mark all as read error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// حذف إشعار
router.delete('/:id', authMiddleware, async (req, res) => {
    try {
        const success = await NotificationService.deleteNotification(
            req.params.id,
            req.user.id
        );

        if (success) {
            res.json({
                success: true,
                message: 'Notification deleted'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'Notification not found'
            });
        }

    } catch (error) {
        console.error('Delete notification error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// إنشاء إشعار جديد (للمدراء فقط)
router.post('/', authMiddleware, async (req, res) => {
    try {
        // التحقق من صلاحية المدير
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Only administrators can create notifications'
            });
        }

        const {
            userIds,
            role,
            title,
            message,
            type = 'info',
            priority = 'medium',
            entityType,
            entityId,
            actionUrl
        } = req.body;

        let notifications;

        if (userIds && userIds.length > 0) {
            // إرسال لمستخدمين محددين
            notifications = await NotificationService.createBulkNotification(
                userIds,
                { title, message, type, priority, entityType, entityId, actionUrl }
            );
        } else if (role) {
            // إرسال لجميع المستخدمين بدور معين
            notifications = await NotificationService.createRoleNotification(
                role,
                { title, message, type, priority, entityType, entityId, actionUrl }
            );
        } else {
            return res.status(400).json({
                success: false,
                message: 'Either userIds or role must be specified'
            });
        }

        res.status(201).json({
            success: true,
            message: 'Notifications created successfully',
            data: notifications
        });

    } catch (error) {
        console.error('Create notification error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// تشغيل الفحوصات التلقائية (للمدراء فقط)
router.post('/run-checks', authMiddleware, async (req, res) => {
    try {
        // التحقق من صلاحية المدير
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Only administrators can run automatic checks'
            });
        }

        const results = await NotificationService.runAutomaticChecks();

        res.json({
            success: true,
            message: 'Automatic checks completed',
            data: results
        });

    } catch (error) {
        console.error('Run checks error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
