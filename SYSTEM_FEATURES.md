# 🏗️ نظام إدارة المقاولات - دليل الميزات الشامل

## 📋 نظرة عامة

نظام شامل لإدارة المقاولات والمشاريع الإنشائية مع ميزات متقدمة للمحاسبة وإدارة المخزون والموارد البشرية.

## 🎯 الوحدات الرئيسية

### 1. 📊 لوحة التحكم
- **لوحة التحكم المحسنة**: عرض شامل للإحصائيات والمؤشرات
- **لوحة التحكم المبسطة**: واجهة سهلة للمستخدمين الجدد
- **التنبيهات الذكية**: تنبيهات الطقس والمشاريع المتأخرة
- **الإحصائيات المباشرة**: أرقام محدثة لحظياً

### 2. 💰 المحاسبة العامة
- **شجرة الحسابات**: إدارة شاملة للحسابات المحاسبية
- **القيود اليومية**: إدخال وإدارة القيود المحاسبية
- **دفتر الأستاذ العام**: عرض حركات الحسابات
- **ميزان المراجعة**: التحقق من توازن الحسابات
- **القوائم المالية**: قائمة الدخل والمركز المالي
- **الترحيل التلقائي**: أتمتة عمليات الترحيل

### 3. 🏗️ إدارة المشاريع
- **إنشاء المشاريع**: نموذج شامل لإضافة مشاريع جديدة
- **تتبع التقدم**: مراقبة تقدم المشاريع والمهام
- **مراكز التكلفة**: تحليل تكاليف المشاريع
- **المستخلصات**: إدارة مستخلصات المشاريع
- **تحليل الميزانية**: مقارنة المخطط مع الفعلي
- **التقارير المتقدمة**: تقارير مفصلة للمشاريع

### 4. 💸 إدارة العهد والمصروفات
- **العهد الشخصية**: إدارة عهد الموظفين
- **المصروفات العمومية**: تتبع المصروفات الإدارية
- **التسوية التلقائية**: أتمتة تسوية العهد
- **التقارير المالية**: تقارير شاملة للمصروفات
- **التحليلات الذكية**: تحليل أنماط الإنفاق

### 5. 📦 إدارة المخزون (جديد!)
- **إدارة الأصناف**: قاعدة بيانات شاملة للمواد
- **تتبع المخزون**: مراقبة مستويات المخزون
- **حركات المخزون**: تسجيل الاستلام والصرف
- **التنبيهات الذكية**: تنبيهات المخزون المنخفض
- **إدارة الموردين**: قاعدة بيانات الموردين
- **تقارير المخزون**: تقارير مفصلة للمخزون

### 6. 👥 إدارة المستخدمين والصلاحيات (جديد!)
- **إضافة المستخدمين**: نموذج شامل لإضافة مستخدمين
- **إدارة الأدوار**: تحديد أدوار المستخدمين
- **الصلاحيات المفصلة**: تحكم دقيق في الصلاحيات
- **تفعيل/تعطيل المستخدمين**: إدارة حالة المستخدمين
- **سجل النشاط**: تتبع نشاط المستخدمين
- **تقارير المستخدمين**: إحصائيات شاملة

### 7. 👨‍💼 الموارد البشرية
- **إدارة الموظفين**: قاعدة بيانات الموظفين
- **تتبع الحضور**: نظام الحضور والانصراف
- **حساب الرواتب**: نظام الرواتب والمكافآت
- **إدارة الإجازات**: طلبات وموافقات الإجازات
- **تقييم الأداء**: نظام تقييم الموظفين

### 8. 🧾 نظام الفواتير
- **فواتير المبيعات**: إنشاء وإدارة فواتير المبيعات
- **فواتير المشتريات**: تتبع فواتير الموردين
- **إدارة العملاء**: قاعدة بيانات العملاء
- **تتبع المدفوعات**: مراقبة المدفوعات والمستحقات
- **تقارير الفواتير**: تحليل المبيعات والمشتريات

### 9. 🤖 التحليلات الذكية
- **توقعات الذكاء الاصطناعي**: تنبؤات مالية ذكية
- **تحليل الاتجاهات**: تحليل اتجاهات الأداء
- **التوصيات الذكية**: توصيات لتحسين الأداء
- **تقارير تنبؤية**: تقارير مستقبلية
- **تحليل المخاطر**: تقييم مخاطر المشاريع
- **مؤشرات الأداء الذكية**: KPIs متقدمة

## 🔐 نظام الصلاحيات

### الأدوار المتاحة:
1. **مدير عام** - صلاحيات كاملة على النظام
2. **محاسب** - المحاسبة والتقارير المالية والمصروفات والبنوك
3. **مدير مشاريع** - المشاريع والمخزون والتقارير
4. **مدير موارد بشرية** - الموظفين والرواتب والتقارير
5. **مستخدم عادي** - عرض التقارير فقط

### الصلاحيات المفصلة:
- 🔐 **جميع الصلاحيات** (مدير عام)
- 📊 **المحاسبة** (الحسابات والقيود)
- 🏗️ **المشاريع** (إدارة المشاريع والمقاولات)
- 📦 **المخزون** (إدارة المواد والمخزون)
- 💰 **المصروفات** (العهد والمصروفات)
- 🏦 **البنوك** (الحسابات البنكية)
- 👨‍💼 **الموارد البشرية** (الموظفين والرواتب)
- 📋 **التقارير** (عرض وإنشاء التقارير)
- ⚙️ **الإعدادات** (إعدادات النظام)
- 👥 **إدارة المستخدمين** (إضافة وتعديل المستخدمين)

## 📊 التقارير المتاحة

### تقارير المشاريع:
- تقرير ملخص المشاريع
- تقرير تفصيلي للمشاريع
- التقرير المالي للمشاريع
- تقرير تقدم المشاريع
- تقرير تكاليف المشاريع

### تقارير المصروفات:
- تقرير شامل للعهد والمصروفات
- تقرير العهد الشهري
- تقرير المصروفات العمومية
- تقرير العهد المعلقة

### تقارير المخزون:
- تقرير شامل للمخزون
- تقرير حركات المخزون
- تقرير الأصناف المنخفضة
- تقرير قيمة المخزون

### تقارير المستخدمين:
- تقرير المستخدمين النشطين
- تقرير سجل النشاط
- تقرير الصلاحيات
- تقرير الأقسام

## 🚀 كيفية البدء

### 1. تسجيل الدخول:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 2. إعداد النظام:
1. إضافة المستخدمين وتحديد الصلاحيات
2. إعداد شجرة الحسابات
3. إضافة أصناف المخزون
4. إنشاء المشاريع الأولى

### 3. الاستخدام اليومي:
1. مراجعة لوحة التحكم
2. إدخال القيود المحاسبية
3. تحديث تقدم المشاريع
4. مراجعة التقارير

## 🔧 الميزات التقنية

- **واجهة مستخدم حديثة**: React + Tailwind CSS
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **أمان متقدم**: نظام صلاحيات مفصل
- **أداء عالي**: تحميل سريع وسلس
- **سهولة الاستخدام**: واجهة بديهية باللغة العربية

## 📞 الدعم والمساعدة

- راجع ملف `كيفية_التشغيل.txt` للتشغيل
- راجع ملف `FIXES_SUMMARY.md` للإصلاحات
- راجع ملف `README_AR.md` للتفاصيل التقنية

---

**النظام جاهز للاستخدام الفوري! 🎉**
