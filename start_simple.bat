@echo off
title Construction ERP System - Simple Start
color 0B

echo ========================================
echo    Construction ERP System
echo    Simple Start Mode
echo ========================================
echo.

echo Starting backend server...
cd server
start "Backend Server" cmd /k "npm start"

echo Waiting for server...
timeout /t 5 /nobreak >nul

echo Starting frontend...
cd ..\client
start "Frontend Client" cmd /k "npm run dev"

echo Waiting for frontend...
timeout /t 8 /nobreak >nul

echo Opening browser...
start http://localhost:5173

echo.
echo ========================================
echo System Started!
echo ========================================
echo Backend:  http://localhost:3001
echo Frontend: http://localhost:5173
echo Login:    admin / admin123
echo ========================================
echo.

pause
