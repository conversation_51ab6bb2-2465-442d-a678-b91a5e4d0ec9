const express = require('express');
const db = require('../database/connection');

const router = express.Router();

// Get inventory items
router.get('/', async (req, res) => {
    try {
        const items = await db.all(`
            SELECT * FROM inventory_items 
            WHERE is_active = 1 
            ORDER BY item_name
        `);

        res.json({
            success: true,
            data: items
        });
    } catch (error) {
        console.error('Get inventory error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
