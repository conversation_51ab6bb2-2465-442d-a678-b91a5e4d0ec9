import { useState, useEffect } from 'react'
import { EnhancedCard, EnhancedButton, StatusBadge, ProgressBar } from '../components/ui'

function EnhancedDashboard() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [notifications, setNotifications] = useState([
    { id: 1, type: 'warning', message: 'مشروع الفيلا السكنية يحتاج مراجعة الميزانية', time: '10:30 ص' },
    { id: 2, type: 'success', message: 'تم استلام دفعة من العميل أحمد علي', time: '09:15 ص' },
    { id: 3, type: 'info', message: 'موعد اجتماع مع المقاول الساعة 2:00 م', time: '08:45 ص' }
  ])
  const [stats, setStats] = useState({
    totalRevenue: 500000,
    totalExpenses: 300000,
    activeProjects: 5,
    netProfit: 200000,
    monthlyGrowth: 12,
    projectProgress: 46,
    cashFlow: 150000,
    pendingInvoices: 8
  })

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const handleLogout = () => {
    localStorage.removeItem('token')
    window.location.reload()
  }

  // خصائص الذكاء الاصطناعي المتقدمة
  const aiInsights = [
    {
      type: 'cost-prediction',
      title: 'توقع التكاليف',
      message: 'متوقع زيادة تكلفة مواد البناء بنسبة 8% الشهر القادم',
      confidence: 66,
      action: 'يُنصح بشراء المواد مسبقاً'
    },
    {
      type: 'cash-flow',
      title: 'تدفق نقدي',
      message: 'توقع نقص في السيولة خلال 15 يوم',
      confidence: 92,
      action: 'تحصيل المستحقات من العملاء'
    },
    {
      type: 'project-risk',
      title: 'مخاطر المشروع',
      message: 'احتمالية تأخير مشروع الفيلا بسبب الطقس',
      confidence: 78,
      action: 'وضع خطة طوارئ للطقس السيء'
    }
  ]

  // بيانات الوحدات مع ألوان وأيقونات محسنة
  const modules = [
    {
      id: 'accounting',
      name: 'المحاسبة العامة',
      description: 'دليل الحسابات والقيود المحاسبية',
      icon: '📊',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-700',
      link: '/accounting',
      status: 'active',
      features: ['دليل الحسابات', 'القيود المحاسبية', 'ميزان المراجعة', 'القوائم المالية']
    },
    {
      id: 'projects',
      name: 'إدارة المشاريع',
      description: 'تسجيل ومتابعة المشاريع',
      icon: '🏗️',
      color: 'from-orange-500 to-orange-600',
      bgColor: 'from-orange-50 to-orange-100',
      borderColor: 'border-orange-200',
      textColor: 'text-orange-700',
      link: '/projects',
      status: 'active',
      features: ['تتبع المشاريع', 'مراكز التكلفة', 'تقارير التقدم', 'إدارة الموارد']
    },
    {
      id: 'expenses',
      name: 'العهد والمصروفات',
      description: 'إدارة العهد والتسوية التلقائية',
      icon: '💰',
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-50 to-purple-100',
      borderColor: 'border-purple-200',
      textColor: 'text-purple-700',
      link: '/expenses',
      status: 'active',
      features: ['إدارة العهد', 'التسوية التلقائية', 'تتبع الإيصالات', 'التحليلات']
    },
    {
      id: 'inventory',
      name: 'إدارة المخزون',
      description: 'تتبع المواد والأصناف',
      icon: '📦',
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-50 to-green-100',
      borderColor: 'border-green-200',
      textColor: 'text-green-700',
      link: '#',
      status: 'development',
      features: ['إدارة المواد', 'تتبع المخزون', 'طلبات الشراء', 'تقارير المخزون']
    },
    {
      id: 'hr',
      name: 'الموارد البشرية',
      description: 'إدارة الموظفين والرواتب',
      icon: '👥',
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'from-indigo-50 to-indigo-100',
      borderColor: 'border-indigo-200',
      textColor: 'text-indigo-700',
      link: '#',
      status: 'development',
      features: ['إدارة الموظفين', 'الرواتب', 'الحضور والانصراف', 'التقييمات']
    },
    {
      id: 'reports',
      name: 'التقارير والتحليلات',
      description: 'تقارير شاملة وتحليلات ذكية',
      icon: '📈',
      color: 'from-pink-500 to-pink-600',
      bgColor: 'from-pink-50 to-pink-100',
      borderColor: 'border-pink-200',
      textColor: 'text-pink-700',
      link: '#',
      status: 'development',
      features: ['تقارير مالية', 'تحليلات الأداء', 'مؤشرات KPI', 'التنبؤات']
    }
  ]

  // الأنشطة الأخيرة
  const recentActivities = [
    {
      id: 1,
      type: 'journal',
      title: 'قيد محاسبي جديد',
      description: 'تم إنشاء قيد رقم JE-2024-001',
      time: '10 دقائق',
      icon: '📝',
      color: 'text-blue-600'
    },
    {
      id: 2,
      type: 'advance',
      title: 'تسوية عهدة',
      description: 'تم تسوية عهدة ADV-001 تلقائياً',
      time: '25 دقيقة',
      icon: '💰',
      color: 'text-green-600'
    },
    {
      id: 3,
      type: 'project',
      title: 'تحديث مشروع',
      description: 'تم تحديث تقدم مشروع الفيلا السكنية',
      time: '1 ساعة',
      icon: '🏗️',
      color: 'text-orange-600'
    },
    {
      id: 4,
      type: 'report',
      title: 'تقرير مالي',
      description: 'تم إنشاء تقرير الأرباح والخسائر',
      time: '2 ساعة',
      icon: '📊',
      color: 'text-purple-600'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="text-2xl">🏗️</div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  نظام إدارة المقاولات
                </h1>
                <p className="text-xs text-gray-500">
                  {currentTime.toLocaleDateString('ar-EG')} - {currentTime.toLocaleTimeString('ar-EG')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-700">
                مرحباً، <span className="font-semibold">مدير النظام</span>
              </div>
              <EnhancedButton
                variant="secondary"
                icon="⚙️"
                size="sm"
                onClick={() => window.location.href = '/settings'}
              >
                الإعدادات
              </EnhancedButton>
              <EnhancedButton
                variant="error"
                icon="🚪"
                size="sm"
                onClick={handleLogout}
              >
                خروج
              </EnhancedButton>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Smart Notifications */}
        {notifications.length > 0 && (
          <div className="mb-6 space-y-2">
            {notifications.map((notification) => (
              <div key={notification.id} className={`p-4 rounded-lg border-l-4 ${
                notification.type === 'warning' ? 'bg-yellow-50 border-yellow-400 text-yellow-800' :
                notification.type === 'success' ? 'bg-green-50 border-green-400 text-green-800' :
                'bg-blue-50 border-blue-400 text-blue-800'
              } fade-in flex items-center justify-between`}>
                <div className="flex items-center">
                  <span className="text-lg mr-3">
                    {notification.type === 'warning' ? '⚠️' :
                     notification.type === 'success' ? '✅' : 'ℹ️'}
                  </span>
                  <div>
                    <div className="font-medium">{notification.message}</div>
                    <div className="text-sm opacity-75">{notification.time}</div>
                  </div>
                </div>
                <button
                  onClick={() => setNotifications(prev => prev.filter(n => n.id !== notification.id))}
                  className="text-lg opacity-50 hover:opacity-100 transition-opacity"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Welcome Section */}
        <EnhancedCard className="mb-8 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white border-0 fade-in">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold mb-2">
                🎉 مرحباً بك في نظام إدارة المقاولات المتقدم
              </h2>
              <p className="text-blue-100 mb-4 text-lg">
                نظام محاسبي شامل مع التسوية التلقائية والذكاء الاصطناعي
              </p>
              <div className="flex items-center space-x-6">
                <StatusBadge status="success" className="bg-green-500/20 text-green-100 border border-green-400/30">
                  النظام يعمل بكفاءة عالية
                </StatusBadge>
                <StatusBadge status="info" className="bg-blue-500/20 text-blue-100 border border-blue-400/30">
                  آخر تحديث: اليوم
                </StatusBadge>
                <StatusBadge status="warning" className="bg-yellow-500/20 text-yellow-100 border border-yellow-400/30">
                  {stats.pendingInvoices} فاتورة معلقة
                </StatusBadge>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="text-8xl opacity-20">🏗️</div>
            </div>
          </div>
        </EnhancedCard>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <EnhancedCard className="text-center bg-gradient-to-br from-green-50 to-emerald-100 border-green-200 hover:from-green-100 hover:to-emerald-200 fade-in">
            <div className="text-4xl mb-3">💰</div>
            <div className="text-sm text-green-600 font-medium mb-1">إجمالي الإيرادات</div>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {stats.totalRevenue.toLocaleString('ar-EG')} ج.م
            </div>
            <div className="text-xs text-green-600 flex items-center justify-center">
              <span className="mr-1">↗️</span>
              +{stats.monthlyGrowth}% من الشهر الماضي
            </div>
          </EnhancedCard>

          <EnhancedCard className="text-center bg-gradient-to-br from-red-50 to-rose-100 border-red-200 hover:from-red-100 hover:to-rose-200 fade-in">
            <div className="text-4xl mb-3">📊</div>
            <div className="text-sm text-red-600 font-medium mb-1">إجمالي المصروفات</div>
            <div className="text-2xl font-bold text-red-700 mb-2">
              {stats.totalExpenses.toLocaleString('ar-EG')} ج.م
            </div>
            <div className="text-xs text-red-600 flex items-center justify-center">
              <span className="mr-1">↗️</span>
              +5% من الشهر الماضي
            </div>
          </EnhancedCard>

          <EnhancedCard className="text-center bg-gradient-to-br from-blue-50 to-cyan-100 border-blue-200 hover:from-blue-100 hover:to-cyan-200 fade-in">
            <div className="text-4xl mb-3">🏗️</div>
            <div className="text-sm text-blue-600 font-medium mb-1">المشاريع النشطة</div>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {stats.activeProjects} مشاريع
            </div>
            <div className="text-xs text-blue-600">
              <ProgressBar value={stats.projectProgress} variant="info" size="sm" className="mb-1" />
              متوسط التقدم: {stats.projectProgress}%
            </div>
          </EnhancedCard>

          <EnhancedCard className="text-center bg-gradient-to-br from-purple-50 to-violet-100 border-purple-200 hover:from-purple-100 hover:to-violet-200 fade-in">
            <div className="text-4xl mb-3">📈</div>
            <div className="text-sm text-purple-600 font-medium mb-1">صافي الربح</div>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {stats.netProfit.toLocaleString('ar-EG')} ج.م
            </div>
            <div className="text-xs text-purple-600 flex items-center justify-center">
              <span className="mr-1">📊</span>
              هامش ربح: {((stats.netProfit / stats.totalRevenue) * 100).toFixed(1)}%
            </div>
          </EnhancedCard>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Modules Grid */}
          <div className="lg:col-span-2">
            <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
              <span className="text-2xl mr-3">🎯</span>
              وحدات النظام
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {modules.map((module, index) => (
                <EnhancedCard 
                  key={module.id}
                  clickable
                  className={`group bg-gradient-to-br ${module.bgColor} ${module.borderColor} hover:shadow-xl transition-all duration-300 slide-in`}
                  style={{ animationDelay: `${index * 100}ms` }}
                  onClick={() => {
                    if (module.status === 'active') {
                      window.location.href = module.link
                    } else {
                      alert(`🔄 جاري تطوير وحدة ${module.name}...\n\nالميزات القادمة:\n${module.features.map(f => `• ${f}`).join('\n')}`)
                    }
                  }}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="text-4xl mr-4 group-hover:scale-110 transition-transform duration-200">
                        {module.icon}
                      </div>
                      <div>
                        <h4 className={`text-lg font-semibold ${module.textColor} group-hover:text-opacity-80`}>
                          {module.name}
                        </h4>
                        <p className="text-sm text-gray-600 group-hover:text-gray-700">
                          {module.description}
                        </p>
                      </div>
                    </div>
                    <StatusBadge 
                      status={module.status === 'active' ? 'success' : 'warning'}
                      className="text-xs"
                    >
                      {module.status === 'active' ? 'متاح' : 'قيد التطوير'}
                    </StatusBadge>
                  </div>
                  
                  <div className="text-xs text-gray-500 mb-3">
                    الميزات الرئيسية:
                  </div>
                  <div className="grid grid-cols-2 gap-1 text-xs">
                    {module.features.slice(0, 4).map((feature, idx) => (
                      <div key={idx} className="flex items-center text-gray-600">
                        <span className="text-green-500 mr-1">✓</span>
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <div className={`mt-4 text-right opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${module.textColor}`}>
                    ←
                  </div>
                </EnhancedCard>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Activities */}
            <EnhancedCard className="fade-in">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="text-xl mr-2">⚡</span>
                الأنشطة الأخيرة
              </h4>
              <div className="space-y-3">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className={`text-lg ${activity.color}`}>
                      {activity.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm text-gray-900">
                        {activity.title}
                      </div>
                      <div className="text-xs text-gray-600">
                        {activity.description}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        منذ {activity.time}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </EnhancedCard>

            {/* AI Insights */}
            <EnhancedCard className="fade-in bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="text-xl mr-2">🤖</span>
                رؤى الذكاء الاصطناعي
              </h4>
              <div className="space-y-3">
                {aiInsights.map((insight, index) => (
                  <div key={index} className="p-3 bg-white rounded-lg border border-purple-100 hover:border-purple-200 transition-colors">
                    <div className="flex items-start justify-between mb-2">
                      <div className="font-medium text-sm text-gray-900">
                        {insight.title}
                      </div>
                      <div className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                        {insight.confidence}% دقة
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 mb-2">
                      {insight.message}
                    </div>
                    <div className="text-xs text-purple-600 font-medium">
                      💡 {insight.action}
                    </div>
                  </div>
                ))}
              </div>
            </EnhancedCard>

            {/* Quick Actions */}
            <EnhancedCard className="fade-in">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span className="text-xl mr-2">🚀</span>
                إجراءات سريعة
              </h4>
              <div className="space-y-3">
                <EnhancedButton
                  variant="primary"
                  icon="📝"
                  className="w-full justify-start"
                  onClick={() => alert('🔄 جاري إنشاء قيد جديد...')}
                >
                  إنشاء قيد محاسبي
                </EnhancedButton>
                <EnhancedButton
                  variant="success"
                  icon="💰"
                  className="w-full justify-start"
                  onClick={() => window.location.href = '/expenses'}
                >
                  إضافة عهدة جديدة
                </EnhancedButton>
                <EnhancedButton
                  variant="warning"
                  icon="📊"
                  className="w-full justify-start"
                  onClick={() => alert('📊 جاري إنشاء تقرير مالي...')}
                >
                  تقرير مالي سريع
                </EnhancedButton>
                <EnhancedButton
                  variant="secondary"
                  icon="🏗️"
                  className="w-full justify-start"
                  onClick={() => window.location.href = '/projects'}
                >
                  متابعة المشاريع
                </EnhancedButton>
              </div>
            </EnhancedCard>

            {/* System Status */}
            <EnhancedCard className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200 fade-in">
              <h4 className="text-lg font-semibold text-green-800 mb-4 flex items-center">
                <span className="text-xl mr-2">💚</span>
                حالة النظام
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-green-700">الخادم</span>
                  <StatusBadge status="success">متصل</StatusBadge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-green-700">قاعدة البيانات</span>
                  <StatusBadge status="success">نشطة</StatusBadge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-green-700">النسخ الاحتياطي</span>
                  <StatusBadge status="success">محدث</StatusBadge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-green-700">الأمان</span>
                  <StatusBadge status="success">محمي</StatusBadge>
                </div>
              </div>
            </EnhancedCard>
          </div>
        </div>
      </main>
    </div>
  )
}

export default EnhancedDashboard
